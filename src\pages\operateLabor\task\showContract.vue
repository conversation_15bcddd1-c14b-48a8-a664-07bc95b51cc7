<template>
  <article>
    <div class="view-picture" style="padding-top: 50px">
      <iframe
        :src="`/pdfViewer.html?pdf=${pdfUrl}`"
        style="width: 100vw; height: calc(100vh - 60px); border: none"
      ></iframe>
    </div>
    <div
      style="
        position: fixed;
        bottom: 0;
        width: 100%;
        height: 50px;
        background: #fff;
        text-align: center;
      "
    >
      <Button type="primary" @click="handleSign">签署</Button>
    </div>
  </article>
</template>
<script>
// import * as pdfjsLib from 'pdfjs-dist/build/pdf'
// import 'pdfjs-dist/web/pdf_viewer.css'
import { ImagePreview, Button } from 'vant'

export default {
  components: {
    ImagePreview,
    Button
  },
  data() {
    return {
      pdfUrl: ''
    }
  },
  created() {
    let archiveId = this.$route.query.archiveId
    console.log('archiveId===', archiveId)
    this.pdfUrl = `${window.env?.apiPath}/api/public/previewFile/${archiveId}`
  },
  methods: {
    viewPicture() {
      ImagePreview([this.pdfUrl])
    },
    handleSign() {
      this.$router.push({
        path: '/laborContractSign',
        query: {
          protocolId: this.$route.query.protocolId
        }
      })
    }
  }
}
</script>
