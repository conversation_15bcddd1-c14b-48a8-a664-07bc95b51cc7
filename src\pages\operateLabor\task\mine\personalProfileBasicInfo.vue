<template>
  <div class="basicInfo" style="color: #A5A5A5">
    <Cell title="姓名" :label="info.name" />
    <Cell title="证件类型" label="居民身份证" />
    <Cell title="证件号码" :label="info.idCard" />
    <Cell title="手机号码" :label="info.cellphone" />
    <Cell title="性别" :label="info.gender" />
    <Cell title="出生日期" :label="info.birthdayDate" />
    <Cell title="年龄" :label="info.age" />
  </div>
</template>
<script>
import { Cell } from 'vant'
import handleError from 'kit/helpers/handleErrorH5'
import makeClient from 'kit/services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    Cell
  },
  data() {
    return {
      info: {
        name: '',
        idCard: '',
        cellphone: '',
        gender: '',
        birthdayDate: '',
        age: ''
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    async init() {
      const { laborInfoId } = this.$route.query
      const [err, r] = await client.apiLaborLaborDetail(laborInfoId)
      if(err) return handleError(err)
      this.info = r.data.basicInfo
    }
  }
}
</script>