<template>
  <div class="salary-calculate-container">
    <!-- 搜索条件 -->
    <div class="search-container">
      <el-form :model="conditions" inline class="search-form">
        <div class="search-header">
          <div class="search-main">
             <el-form-item>
            <el-select
              v-model="conditions.filters.contractId"
              placeholder="请选择服务合同"
              clearable
              filterable
            >
              <el-option
                v-for="item in contractOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
           <el-form-item label="" class="search-item">
            <el-date-picker
              v-model="conditions.filters.taxPaymentPeriod"
              type="month"
              placeholder="请选择税款所属期"
              value-format="yyyy-MM"
              class="search-date"
              clearable
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-select
              v-model="conditions.filters.status"
              placeholder="请选择状态"
              clearable
            >
              <el-option
                v-for="(label, value) in statusOptions"
                :key="value"
                :label="label"
                :value="value"
              />
            </el-select>
          </el-form-item>
          </div>
          <div class="search-actions">
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button @click="onReset">重置</el-button>
          </div>
        </div>
    
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <!-- 表头操作区 -->
      <div class="table-header">
        <div class="table-actions">
          <el-button type="primary" @click="handleAdd">
            <i class="el-icon-plus"></i>
            新增工资表
          </el-button>
        </div>
      </div>

      <el-table
        :data="data"
        v-loading="loading"
        :height="tableHeight"
        :stripe="false"
        highlight-current-row
        :header-cell-style="{background:'#f5f7fa', color: '#303133', fontWeight: '550'}"
        row-key="id"
      >
        <template slot="empty">
          <div class="empty-data">暂无数据</div>
        </template>
        <el-table-column prop="id" label="工资表ID" min-width="100" />
        <el-table-column prop="contractName" label="服务合同名称" width="160" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.contractName || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="taxPeriod" label="税款所属期" min-width="100">
          <template slot-scope="scope">
            {{ scope.row.taxPeriod || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="totalPeople" label="总人数" min-width="80">
          <template slot-scope="scope">
            {{ scope.row.totalPeople || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="totalPayable" label="应发总计" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.totalPayable ? `${scope.row.totalPayable.toLocaleString()}` : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="totalIncomeTax" label="个税总计" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.totalIncomeTax ? `${scope.row.totalIncomeTax.toLocaleString()}` : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="totalVat" label="增值税总计" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.totalVat ? `${scope.row.totalVat.toLocaleString()}` : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="totalSurtax" label="附加税总计" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.totalSurtax ? `${scope.row.totalSurtax.toLocaleString()}` : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="netPaymentTotal" label="实发总计" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.netPaymentTotal ? `${scope.row.netPaymentTotal.toLocaleString()}` : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="taxDeclarationMonth" label="申报月" min-width="80">
          <template slot-scope="scope">
            {{ scope.row.taxDeclarationMonth || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="uploadTime" label="上传时间" min-width="160" :formatter="formatDateTime" />
        <el-table-column prop="status" label="状态" min-width="100">
          <template slot-scope="scope">
            <el-tag
              :type="getStatusTagType(scope.row.status)"
              size="small"
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120" fixed="right">
          <template slot-scope="scope">
            <div class="action-buttons">
              <el-button
                type="text"
                size="small"
                @click="handleView(scope.row)"
              >
                查看
              </el-button>
              <el-button
                type="text"
                v-if="scope.row.status === 'CALCULATING'"
                size="small"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :current-page="Math.floor(conditions.offset / conditions.limit) + 1"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="conditions.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </div>
  </div>
</template>

<script>
import makeClient from '../../../services/operateLabor/makeClient'

const client = makeClient()


export default {
  data() {
    return {
      createTimeRange: [],
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        filters: {
          supplierCorporationId: '',
          customerId: [],
          contractId: '',
          status: '',
          createTimeStart: null,
          createTimeEnd: null
        }
      },
      total: 0,
      data: [],
      loading: true,
      showMoreFilters: false,
      tableHeight: 500,

      supplierOptions: [],
      customerOptions: [],
      contractOptions: [],
      statusOptions: {
        'CALCULATING': '算税中',
        'UNCONFIRMED': '待确认',
        'CONFIRMED': '已确认',
      }
    }
  },
  async created() {
    await this.loadOptions()
    await this.getList()
    this.setTableHeight()
    window.addEventListener('resize', this.setTableHeight)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.setTableHeight)
  },
  methods: {
    async loadOptions() {
      // 加载合同选项
      const [err, res] = await client.customerGetAllContracts({ body: {} })
      if (!err) {
        this.contractOptions = res.data || []
      }
    },

    async getList() {
      this.loading = true

      const [err, r] = await client.customerSalaryListPayroll({
        body: this.conditions
      })

      this.loading = false

      if (err) {
        console.error('加载列表失败：', err)
        this.$message.error('加载列表失败，请重试')
        return
      }

      this.data = r.data.list || []
      this.total = r.data.total || 0
      this.$nextTick(() => {
        this.setTableHeight()
      })
    },

    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },

    handleSizeChange(size) {
      this.conditions.limit = size
      this.conditions.offset = 0
      this.getList()
    },

    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },

    onReset() {
      this.conditions = {
        offset: 0,
        limit: 10,
        withTotal: true,
        filters: {
          supplierCorporationId: '',
          customerId: [],
          contractId: '',
          status: '',
          createTimeStart: null,
          createTimeEnd: null
        }
      }
      this.createTimeRange = []
      this.getList()
    },

    formatDateTime(row, column, cellValue) {
      if (!cellValue) return '-'
      return new Date(cellValue).toLocaleString('zh-CN')
    },

    setTableHeight() {
      try {
        const windowHeight = window.innerHeight;
        const searchContainer = this.$el?.querySelector('.search-container');
        const searchHeight = searchContainer ? searchContainer.offsetHeight : 0;
        const tableHeader = this.$el?.querySelector('.table-header');
        const tableHeaderHeight = tableHeader ? tableHeader.offsetHeight : 0;
        const pagination = this.$el?.querySelector('.pagination-container');
        const paginationHeight = pagination ? pagination.offsetHeight : 40;
        const padding = 40;

        const availableHeight = windowHeight - searchHeight - tableHeaderHeight - paginationHeight - padding;

        if (this.data.length <= 5) {
          this.tableHeight = 500;
        } else {
          const minHeight = 300;
          const maxHeight = windowHeight - searchHeight - tableHeaderHeight - paginationHeight - padding - 5;
          this.tableHeight = Math.min(maxHeight, Math.max(availableHeight, minHeight));
        }

        if (this.tableHeight) {
          this.tableHeight = Math.floor(this.tableHeight);
        }
      } catch (error) {
        console.warn('设置表格高度时出错:', error);
        this.tableHeight = 500; // 设置默认高度
      }
    },

    getStatusText(status) {
      const statusMap = {
        'CALCULATING': '算税中',
        'UNCONFIRMED': '待确认',
        'CONFIRMED': '已确认',
        'DRAFT': '草稿',
        'SUBMITTED': '已提交',
        'APPROVED': '已审核',
        'PAID': '已发放'
      }
      return statusMap[status] || status || '-'
    },

    getStatusTagType(status) {
      const typeMap = {
        'CALCULATING': 'warning',
        'UNCONFIRMED': 'warning',
        'CONFIRMED': 'success',
        'DRAFT': 'info',
        'SUBMITTED': 'warning',
        'APPROVED': 'success',
        'PAID': 'success'
      }
      return typeMap[status] || 'info'
    },

    handleAdd() {
      this.$router.push('/salaryCalculate/new')
    },

    handleView(row) {
      this.$router.push(`/salaryCalculate/${row.id}`)
    },

    async handleDelete(row) {
      this.$confirm('此操作将永久删除该工资表, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          try {
            this.loading = true
            const [err] = await client.customerSalaryDeletePayroll({
              body: {
                id: row.id
              }
            })
            if (err) {
              console.error('删除工资表失败：', err)
              this.$message.error(err.message || '删除失败，请重试')
              return
            }
            this.$message.success('删除工资表成功')
            this.getList() // 刷新列表
          } catch (error) {
            console.error('删除工资表异常：', error)
            this.$message.error('删除失败，请重试')
          } finally {
            this.loading = false
          }
        })
        .catch(() => {})
    },

    handleCreateTimeChange(value) {
      if (value && value.length === 2) {
        this.conditions.filters.createTimeStart = value[0]
        this.conditions.filters.createTimeEnd = value[1]
      } else {
        this.conditions.filters.createTimeStart = null
        this.conditions.filters.createTimeEnd = null
      }
    },

    toggleMoreFilters() {
      this.showMoreFilters = !this.showMoreFilters
      this.$nextTick(() => {
        this.setTableHeight()
      })
    },


  }
}
</script>

<style scoped>
.salary-calculate-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  background-color: #f5f7fa;
  padding: 12px;
  box-sizing: border-box;
}

.search-container {
  background: #fff;
  padding: 20px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  position: relative;
}

.search-form {
  position: relative;
}

.search-header {
  position: relative;
  padding-right: 200px;
  min-height: 40px;
}

.search-main {
  display: flex;
  flex-wrap: wrap;
  gap: 16px 24px;
  align-items: center;
}

.search-main .el-form-item {
  margin: 0;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.search-main .el-form-item .el-form-item__label {
  margin-right: 8px;
  font-weight: 500;
  color: #606266;
  flex-shrink: 0;
}

.search-main .el-form-item .el-form-item__content {
  width: 200px;
}

.search-actions {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  height: 40px;
}

.search-actions .el-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
}

.search-actions .el-button--primary {
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
}

.search-actions .el-button--primary:hover {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
}

.el-form-item .el-form-item__label {
  font-size: 14px;
  color: #606266;
  font-weight: 550;
  line-height: 32px;
}

.el-form-item .el-input,
.el-form-item .el-select,
.el-form-item .el-date-editor {
  width: 100%;
}

.more-form-items {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.more-form-items .el-form-item {
  width: calc(50% - 20px);
  margin-right: 20px;
  margin-bottom: 16px;
  box-sizing: border-box;
}

.more-form-items .el-date-editor.el-input__inner,
.more-form-items .el-date-editor.el-input,
.more-form-items .el-date-editor.el-range-editor {
  width: 100%;
  min-width: 300px;
}

.toggle-btn {
  padding: 0;
  font-size: 14px;
  margin-left: 16px;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
}

.toggle-btn:hover {
  transform: translateY(-1px);
}

.table-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-bottom: 12px;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions .el-button {
  border-radius: 4px;
  padding: 9px 20px;
  transition: all 0.3s;
}

.table-actions .el-button--primary {
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
}

.table-actions .el-button--primary:hover {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  transform: translateY(-1px);
}

.table-container {
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.05);
  flex: 1;
  overflow: hidden;
}







/* 表格基本样式 */
.el-table {
  width: 100%;
  margin-bottom: 5px;
  border: none;
}

/* 处理空数据状态 */
.el-table__empty-block {
  width: 100% !important;
}

.el-table th,
.el-table--medium th {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 550;
  text-align: left;
  padding: 12px 0;
  border-bottom: 1px solid #EBEEF5;
  font-size: 14px;
}

.el-table td,
.el-table--medium td {
  padding: 12px 0;
  color: #606266;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.action-buttons .el-button {
  margin: 0;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 3px;
  transition: all 0.2s;
  min-width: 48px;
  height: 24px;
  line-height: 16px;
}

.action-buttons .el-button--text {
  background: transparent;
  border: none;
  padding: 4px 8px;
}

.action-buttons .el-button--text:hover {
  background: #ecf5ff;
  transform: translateY(-1px);
}

.empty-data {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 40px 0;
  width: 100%;
}

.pagination-container {
  padding: 12px 0 0;
  text-align: right;
  background: #fff;
  margin-top: auto;
}

/* 状态标签样式 */
.status-tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  display: inline-block;
}



/* 响应式调整 */
@media screen and (max-width: 1400px) {
  .more-form-items .el-form-item {
    width: calc(50% - 20px);
  }
}

@media screen and (max-width: 1200px) {
  .search-header {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .search-main {
    flex: 1;
    min-width: 70%;
    margin-bottom: 16px;
  }

  .search-actions {
    justify-content: flex-end;
    min-width: 200px;
  }

  .more-form-items .el-form-item {
    width: calc(100% - 20px);
  }
}

/* 响应式调整 */
@media screen and (max-width: 1400px) {
  .search-main {
    margin-right: 180px;
  }

  .more-form-items .el-form-item {
    width: calc(33.33% - 20px);
  }
}

@media screen and (max-width: 1200px) {
  .search-main {
    margin-right: 160px;
  }

  .search-main .el-form-item {
    min-width: 260px;
  }

  .more-form-items .el-form-item {
    width: calc(50% - 20px);
  }
}

@media screen and (max-width: 992px) {
  .search-main {
    margin-right: 140px;
  }

  .search-main .el-form-item {
    min-width: 240px;
    margin-bottom: 12px;
  }

  .search-main .el-form-item .el-input,
  .search-main .el-form-item .el-select {
    width: 200px;
  }
}

@media screen and (max-width: 768px) {
  .search-header {
    flex-direction: column;
  }

  .search-main {
    margin-right: 0;
    margin-bottom: 16px;
    width: 100%;
  }

  .search-main .el-form-item {
    width: 100%;
    margin-right: 0;
    margin-bottom: 12px;
    min-width: auto;
  }

  .search-main .el-form-item .el-input,
  .search-main .el-form-item .el-select {
    width: 100%;
  }

  .search-actions {
    position: static;
    justify-content: flex-start;
    width: 100%;
  }

  .search-actions .el-button {
    margin-left: 0;
    margin-right: 8px;
    margin-bottom: 8px;
  }

  .more-form-items .el-form-item {
    width: 100%;
    margin-right: 0;
  }

  .more-form-items .el-date-editor.el-input__inner,
  .more-form-items .el-date-editor.el-input,
  .more-form-items .el-date-editor.el-range-editor {
    width: 100%;
    min-width: auto;
  }
}

@media screen and (max-width: 480px) {
  .search-actions {
    flex-wrap: wrap;
  }

  .search-actions .el-button {
    flex: 1;
    min-width: 60px;
    margin-bottom: 8px;
  }
}
</style>
