<template>
  <div
    class="projectExperience"
    style="min-height: 100vh; overflow: auto"
  >
    <Icon name="add" @click="addExperience" />
    <div v-for="(item, index) in experienceList" :key="index">
      <div></div>
      <Field v-model="item.projectName" label="项目名称" placeholder="请输入" />
      <Field v-model="item.post" label="担任角色" placeholder="请输入" />
      <Field v-model="value" label="文本" placeholder="请输入用户名" />
      <Field v-model="item.description" label="项目描述" placeholder="请输入" />
      <Field v-model="item.performance" label="项目业绩" placeholder="请输入" />
    </div>
  </div>
</template>
<script>
import { Icon, Field } from 'vant'
import handleError from 'kit/helpers/handleErrorH5'
import makeClient from 'kit/services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    Icon,
    Field
  },
  data() {
    return {
      experienceList: []
    }
  },
  created() {
    this.init()
  },
  methods: {
    async init() {
      const { laborInfoId } = this.$route.query
      const [err, r] = await client.apiLaborLaborDetail(laborInfoId)
      if (err) return handleError(err)
      this.experienceList = r.data.projectHistory
    },
    addExperience() {
      this.experienceList.push({
        key: Date.now(),
        projectName: '',
        post: '',
        projectStart: '',
        projectEnd: '',
        description: '',
        performance: ''
      })
    }
  }
}
</script>
