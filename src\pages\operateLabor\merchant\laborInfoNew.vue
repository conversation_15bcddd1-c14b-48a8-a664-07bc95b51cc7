<template>
  <div class="labor-new-container" v-loading="pageLoading" element-loading-text="加载中...">
    <div class="form-container">
      <el-form :model="form" :rules="rules" ref="form" label-width="120px" class="labor-form">
        
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-title">{{ isEditMode ? '编辑人员' : '添加人员' }}</div>
          
          <el-row :gutter="40">
            <el-col :span="8">
              <el-form-item label="姓名" prop="name">
                <el-input
                  v-model="form.name"
                  placeholder="请输入姓名"
                  maxlength="20"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="身份证件号码" prop="idCard">
                <el-input
                  v-model="form.idCard"
                  placeholder="请输入身份证件号码"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="手机号" prop="cellPhone">
                <el-input
                  v-model="form.cellPhone"
                  placeholder="请输入手机号"
                  @input="handlePhoneInput"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="40">
            <el-col :span="8">
              <el-form-item label="银行卡号" prop="bankCard">
                <el-input
                  v-model="form.bankCard"
                  placeholder="请输入银行卡号"
                  @input="handleBankCardInput"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="开户行" prop="cardBank">
                <el-input
                  v-model="form.cardBank"
                  placeholder="请输入开户行"
                  maxlength="64"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 项目经历 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-title">项目经历</div>
            <el-button
              type="text"
              size="small"
              @click="addProjectExperience"
              class="add-project-btn"
            >
              <i class="el-icon-plus add-icon"></i>
              添加
            </el-button>
          </div>

          <div
            v-for="(project, index) in form.projectExperiences"
            :key="index"
            class="project-item"
          >
            <div class="project-header">
              <el-button
                v-if="form.projectExperiences.length > 1"
                type="text"
                size="small"
                @click="removeProjectExperience(index)"
                class="delete-project-btn"
              >
                <i class="el-icon-close"></i>
              </el-button>
            </div>
            <el-row :gutter="40">
              <el-col :span="12">
                <el-form-item label="项目名称">
                  <el-input
                    v-model="project.projectName"
                    placeholder="请输入项目名称"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="担任角色">
                  <el-input
                    v-model="project.role"
                    placeholder="请输入担任角色"
                  />
                </el-form-item>
              </el-col>
             
            </el-row>

            <el-row :gutter="40">
              <el-col :span="12">
                <el-form-item label="项目时间">
                  <el-date-picker
                    v-model="project.projectTime"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="项目描述">
                  <el-input
                    v-model="project.description"
                    type="textarea"
                    :rows="2"
                    placeholder="请输入项目描述"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目业绩">
                  <el-input
                    v-model="project.achievement"
                    type="textarea"
                    :rows="2"
                    placeholder="请输入项目业绩"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 附件信息 -->
        <div class="form-section">
          <div class="section-title">附件信息</div>
          <div style="margin-left: 20px;" class="upload-section">
            <div class="upload-tip">
              可上传证件照、技能证书、简历附件等，支持JPG、PNG、JPEG、PDF文件格式，不超过10MB
            </div>
            <FileUploadComponent
              ref="fileUploadComponent"
              :initial-files="form.attachments"
              @files-change="handleFilesChange"
            />
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button @click="onCancel">取消</el-button>
          <el-button type="primary" @click="onSubmit" :loading="submitting">
            {{ isEditMode ? '保存' : '确定' }}
          </el-button>
        </div>

      </el-form>
    </div>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import handleSuccess from '../../../helpers/handleSuccess'
import makeClient from '../../../services/operateLabor/makeClient'
import FileUploadComponent from './components/FileUploadComponent.vue'

const client = makeClient()

export default {
  components: {
    FileUploadComponent
  },
  data() {
    return {
      pageLoading: true,
      submitting: false,
      form: {
        name: '',
        idCard: '',
        cellPhone: '',
        supplierId: '',
        contractId: '',
        bankCard: '',
        cardBank: '',
        projectExperiences: [
          {
            projectName: '',
            role: '',
            development: '',
            projectTime: null,
            description: '',
            achievement: ''
          }
        ],
        attachments: []
      },
      rules: {
        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        idCard: [
          { required: true, message: '请输入身份证件号码', trigger: 'blur' },
          { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '请输入正确的身份证号码', trigger: 'blur' }
        ],
        cellPhone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        bankCard: [{ required: true, message: '请输入银行卡号', trigger: 'blur' }],
        cardBank: [{ required: true, message: '请输入开户行', trigger: 'blur' }]
      }
    }
  },
  computed: {
    isEditMode() {
      return !!(this.$route.params.id || this.$route.query.id)
    }
  },
  async created() {
    // 检查是否为编辑模式
    const laborId = this.$route.params.id || this.$route.query.id
    if (laborId) {
      await this.loadLaborData(laborId)
    }
    this.pageLoading = false
  },
  methods: {
    // 加载人员数据（编辑模式）
    async loadLaborData(laborId) {
      try {
        const [err, response] = await client.customerGetLaborDetail({
          path: { id: laborId }
        })

        if (err) {
          handleError(err)
          return
        }

        if (response && response.data) {
          const data = response.data

          // 填充基本信息
          this.form = {
            ...this.form,
            name: data.name || '',
            idCard: data.idCard || '',
            cellPhone: data.cellPhone || '',
            bankCard: data.bankCard || '',
            cardBank: data.cardBank || '',
            projectExperiences: data.projectExperiences && data.projectExperiences.length > 0
              ? data.projectExperiences
              : [{ projectName: '', role: '', development: '', projectTime: null, description: '', achievement: '' }]
          }

          // 处理附件信息
          if (data.attachments && data.attachments.length > 0) {
            this.form.attachments = data.attachments.map(attachment => ({
              name: attachment.fileName || attachment.name,
              fileId: attachment.fileId || attachment.id,
              url: attachment.url
            }))
          }
        }
      } catch (error) {
        console.error('加载人员数据失败：', error)
        this.$message.error('加载数据失败')
      }
    },

    handlePhoneInput() {
      // 只允许输入数字
      this.form.cellPhone = this.form.cellPhone.replace(/[^\d]/g, '')
    },

    handleBankCardInput() {
      // 只允许输入数字
      this.form.bankCard = this.form.bankCard.replace(/[^\d]/g, '')
    },

    // 项目经历相关方法
    addProjectExperience() {
      this.form.projectExperiences.push({
        projectName: '',
        role: '',
        development: '',
        projectTime: null,
        description: '',
        achievement: ''
      })
    },

    removeProjectExperience(index) {
      if (this.form.projectExperiences.length > 1) {
        this.form.projectExperiences.splice(index, 1)
      }
    },

    // 文件上传相关方法
    handleFilesChange(files) {
      this.form.attachments = files
    },

    async onSubmit() {
      const valid = await this.$refs.form.validate()
      if (!valid) {
        return
      }

      this.submitting = true

      try {
        // 获取上传文件的ID数组
        let attachmentIds = []
        if (this.$refs.fileUploadComponent) {
          attachmentIds = await this.$refs.fileUploadComponent.getFileIds()
        }

        // 处理项目经历数据
        const formData = {
          ...this.form,
          projectExperiences: this.form.projectExperiences.filter(exp =>
            exp.projectName || exp.role || exp.development || exp.description || exp.achievement
          ),
          attachmentIds
        }

        // 判断是新增还是编辑
        const laborId = this.$route.params.id || this.$route.query.id
        let err

        if (laborId) {
          // 编辑模式
          [err] = await client.customerUpdateLaborDetail({
            path: { id: laborId },
            body: formData
          })
        } else {
          // 新增模式
          [err] = await client.customerAddLabor({ body: formData })
        }

        if (err) {
          handleError(err)
          return
        }

        handleSuccess(laborId ? '修改成功' : '添加成功')
        this.$router.push('/laborInfo')
      } catch (error) {
        console.error('提交失败：', error)
        this.$message.error(error.message || '提交失败，请重试')
      } finally {
        this.submitting = false
      }
    },

    onCancel() {
      this.$router.back()
    }
  }
}
</script>

<style scoped>
.labor-new-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 100vh;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  box-sizing: border-box;
}

.form-container {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  width: 100%;
  max-width: 1200px;
  min-height: 500px;
}

.labor-form {
  width: 100%;
}

.form-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 24px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
  display: inline-block;
}

.el-form-item {
  margin-bottom: 24px;
}

.el-form-item__label {
  font-size: 14px;
  color: #606266;
  font-weight: 550;
  line-height: 32px;
}

.el-input,
.el-select {
  width: 100%;
}


.form-actions {
  text-align: center;
  padding-top: 32px;
  border-top: 1px solid #ebeef5;
  margin-top: 40px;
}

.form-actions .el-button {
  margin: 0 12px;
  padding: 12px 32px;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.3s;
}

.form-actions .el-button--primary {
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
}

.form-actions .el-button--primary:hover {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  transform: translateY(-1px);
}

.form-actions .el-button:not(.el-button--primary) {
  color: #606266;
  border-color: #dcdfe6;
  background-color: #fff;
}

.form-actions .el-button:not(.el-button--primary):hover {
  color: #409EFF;
  border-color: #409EFF;
  background-color: #ecf5ff;
  transform: translateY(-1px);
}

/* 项目经历样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
  display: inline-block;
}

.add-project-btn {
  color: #409EFF !important;
  padding: 4px 8px !important;
  margin: 0 !important;
  font-size: 14px;
}

.project-item >>> .el-textarea__inner {
  min-height: 60px !important;
  height: 60px !important;
  resize: none; 
}

.add-project-btn:hover {
  background-color: #ecf5ff !important;
}

.add-icon {
  font-size: 12px;
  font-weight: bold;
  margin-right: 4px;
  border: 1px solid #409EFF;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.project-item {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 20px 60px 0px 20px;
  margin-bottom: 20px;
  position: relative;
}

.project-item:last-child {
  margin-bottom: 0;
}

.project-header {
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 10;
}

.delete-project-btn {
  color: #f56c6c !important;
  padding: 4px !important;
  margin: 0 !important;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-project-btn:hover {
  background-color: rgba(245, 108, 108, 0.2) !important;
  color: #f78989 !important;
}

.delete-project-btn .el-icon-close {
  font-size: 12px;
}

/* 附件上传样式 */
.upload-section {
  margin-top: 16px;
}

.upload-tip {
  color: #909399;
  font-size: 12px;
  margin-bottom: 12px;
  line-height: 1.5;
}

.upload-content {
  text-align: center;
}

.upload-content .el-icon-upload {
  font-size: 67px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.upload-text {
  color: #606266;
  font-size: 14px;
  margin-bottom: 8px;
}

.upload-hint {
  color: #909399;
  font-size: 12px;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .labor-new-container {
    padding: 20px 12px;
  }
  
  .form-container {
    padding: 24px;
  }
  
  .el-form-item__label {
    width: 100px !important;
  }
}
</style>
