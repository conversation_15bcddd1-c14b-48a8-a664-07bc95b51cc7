<template>
  <div class="labor-new-container" v-loading="pageLoading" element-loading-text="加载中...">
    <!-- 编辑按钮 -->
    <div v-if="mode === 'view'" class="edit-button-container">
      <el-button type="primary" @click="toggleEditMode" class="edit-button">
        <i class="el-icon-edit"></i>
        编辑
      </el-button>
    </div>

    <div class="form-container">
      <el-form :model="form" :rules="dynamicRules" ref="form" label-width="120px" class="labor-form">

        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-title">{{ getSectionTitle() }}</div>

          <el-row :gutter="40">
            <el-col :span="8">
              <el-form-item label="姓名" prop="name">
                <el-input
                  v-model="form.name"
                  placeholder="请输入姓名"
                  maxlength="20"
                  :readonly="!isEditable"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="身份证件号码" prop="idCard">
                <el-input
                  v-model="form.idCard"
                  placeholder="请输入身份证件号码"
                  :readonly="!isEditable"
                  :disabled="mode === 'edit'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="手机号" prop="cellPhone">
                <el-input
                  v-model="form.cellPhone"
                  placeholder="请输入手机号"
                  @input="handlePhoneInput"
                  :readonly="!isEditable"
                  :disabled="mode === 'edit'"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 查看/编辑模式下显示的额外字段 -->
          <div v-if="mode !== 'new'">
            <el-row :gutter="40">
              <el-col :span="8">
                <el-form-item label="年龄">
                  <el-input
                    v-model="form.age"
                    placeholder="年龄"
                    :readonly="!isEditable"
                    :disabled="mode === 'edit'"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="性别">
                  <el-input
                    v-model="form.gender"
                    placeholder="性别"
                    :readonly="!isEditable"
                    :disabled="mode === 'edit'"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="证件有效期">
                  <el-input
                    v-model="form.idCardPeriod"
                    placeholder="证件有效期"
                    :readonly="!isEditable"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="40">
              <el-col :span="8">
                <el-form-item label="民族">
                  <el-input
                    v-model="form.nation"
                    placeholder="民族"
                    :readonly="!isEditable"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="详细地址">
                  <el-input
                    v-model="form.address"
                    placeholder="详细地址"
                    :readonly="!isEditable"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <el-row :gutter="40">
            <el-col :span="8">
              <el-form-item label="银行卡号" prop="bankCard">
                <el-input
                  v-model="form.bankCard"
                  placeholder="请输入银行卡号"
                  @input="handleBankCardInput"
                  :readonly="!isEditable"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="开户行" prop="cardBank">
                <el-input
                  v-model="form.cardBank"
                  placeholder="请输入开户行"
                  maxlength="64"
                  :readonly="!isEditable"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 新增模式下显示作业主体、客户、合同选择 -->
          <div v-if="mode === 'new'">
            <el-row :gutter="40">
              <el-col :span="8">
                <el-form-item label="所属作业主体" prop="supplierId">
                  <el-select
                    filterable
                    v-model="form.supplierId"
                    placeholder="请选择所属作业主体"
                    style="width: 100%"
                    @change="onSupplierChange"
                  >
                    <el-option v-for="supplier in supplierOptions" :key="supplier.id" :label="supplier.name"
                      :value="supplier.id">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="所属客户" prop="customerId">
                  <el-select
                    filterable
                    v-model="form.customerId"
                    placeholder="请选择所属客户"
                    style="width: 100%"
                    @change="onCustomerChange"
                  >
                    <el-option v-for="customer in customerOptions" :key="customer.id" :label="customer.name"
                      :value="customer.id">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="所属服务合同" prop="contractId">
                  <el-select
                    filterable
                    v-model="form.contractId"
                    placeholder="请选择所属服务合同"
                    style="width: 100%"
                    :disabled="!form.supplierId || !form.customerId"
                  >
                    <el-option v-for="contract in contractOptions" :key="contract.id" :label="contract.name"
                      :value="contract.id">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 项目经历 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-title">项目经历</div>
            <el-button
              v-if="isEditable"
              type="text"
              size="small"
              @click="addProjectExperience"
              class="add-project-btn"
            >
              <i class="el-icon-plus add-icon"></i>
              添加
            </el-button>
          </div>

          <!-- 查看模式下无项目经历时的提示 -->
          <div v-if="mode === 'view' && (!form.laborProjectHistory || form.laborProjectHistory.length === 0 || !hasProjectData)" class="empty-tip">
            暂无项目经历
          </div>

          <!-- 项目经历列表 -->
          <div
            v-for="(project, index) in form.laborProjectHistory"
            :key="index"
            class="project-item"
            v-show="isEditable || hasProjectData"
          >
            <div class="project-header">
              <el-button
                v-if="isEditable && form.laborProjectHistory.length > 1"
                type="text"
                size="small"
                @click="removeProjectExperience(index)"
                class="delete-project-btn"
              >
                <i class="el-icon-close"></i>
              </el-button>
            </div>
            <el-row :gutter="40">
              <el-col :span="12">
                <el-form-item label="项目名称">
                  <el-input
                    v-model="project.projectName"
                    placeholder="请输入项目名称"
                    :readonly="!isEditable"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="担任角色">
                  <el-input
                    v-model="project.post"
                    placeholder="请输入担任角色"
                    :readonly="!isEditable"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="40">
              <el-col :span="12">
                <el-form-item label="项目时间">
                  <el-date-picker
                    v-model="project.projectTime"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    style="width: 100%"
                    :readonly="!isEditable"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="项目描述">
                  <el-input
                    v-model="project.description"
                    type="textarea"
                    :rows="2"
                    placeholder="请输入项目描述"
                    :readonly="!isEditable"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目业绩">
                  <el-input
                    v-model="project.performance"
                    type="textarea"
                    :rows="2"
                    placeholder="请输入项目业绩"
                    :readonly="!isEditable"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 附件信息 -->
        <div class="form-section">
          <div class="section-title">附件信息</div>
          <div class="upload-section">
            <!-- 查看模式下无附件时的提示 -->
            <div v-if="mode === 'view' && (!form.attachments || form.attachments.length === 0)" class="empty-tip">
              暂无附件信息
            </div>

            <!-- 编辑模式下或有附件时显示提示和组件 -->
            <div style="margin-left: 20px;" v-if="isEditable || (form.attachments && form.attachments.length > 0)">
              <div v-if="isEditable" class="upload-tip">
                可上传证件照、技能证书、简历附件等，支持JPG、PNG、JPEG、PDF文件格式，不超过10MB
              </div>
              <AttachmentDisplayComponent
                ref="attachmentDisplayComponent"
                :initial-file-ids="form.attachments"
                @files-change="handleFilesChange"
                :readonly="!isEditable"
              />
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <!-- 新增模式 -->
          <div v-if="mode === 'new'">
            <el-button @click="onCancel">取消</el-button>
            <el-button type="primary" @click="onSubmit" :loading="submitting">
              确定
            </el-button>
          </div>
          <!-- 编辑模式 -->
          <div v-else-if="mode === 'edit'">
            <el-button @click="onCancel">取消</el-button>
            <el-button type="primary" @click="confirmEdit" :loading="submitting">
              确定
            </el-button>
          </div>
        </div>

      </el-form>
    </div>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import AttachmentDisplayComponent from './components/AttachmentDisplayComponent.vue'

const client = makeClient()

export default {
  components: {
    AttachmentDisplayComponent
  },
  data() {
    return {
      pageLoading: false,
      submitting: false,
      mode: 'new', // 'new', 'view', 'edit'
      laborId: null,
      originalFormData: {}, // 用于取消编辑时恢复数据
      // 选项数据
      supplierOptions: [],
      customerOptions: [],
      contractOptions: [],

      form: {
        name: '',
        idCard: '',
        bankCard: '',
        cardBank: '',
        cellPhone: '',
        age: '',
        gender: '',
        idCardPeriod: '',
        nation: '',
        address: '',
        supplierId: '',
        customerId: '',
        contractId: '',
        laborProjectHistory: [
          {
            projectName: '',
            post: '',
            projectTime: null,
            description: '',
            performance: ''
          }
        ],
        attachments: []
      }
    }
  },
  computed: {
    isEditable() {
      return this.mode === 'new' || this.mode === 'edit'
    },
    // 判断是否有项目经历数据
    hasProjectData() {
      if (!this.form.laborProjectHistory || this.form.laborProjectHistory.length === 0) {
        return false
      }
      return this.form.laborProjectHistory.some(project =>
        project.projectName || project.post || project.description || project.performance
      )
    },
    // 动态验证规则
    dynamicRules() {
      const baseRules = {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          { max: 20, message: '姓名不能超过20个字符', trigger: 'blur' }
        ],
        idCard: [
          { required: true, message: '请输入身份证件号码', trigger: 'blur' },
          { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '请输入正确的18位身份证号码', trigger: 'blur' }
        ],
        cellPhone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的11位手机号码', trigger: 'blur' }
        ],
        bankCard: [
          { pattern: /^[1-9]\d{11,19}$/, message: '请输入正确的12-20位银行卡号', trigger: 'blur' }
        ],
        cardBank: [
          { max: 64, message: '开户行不能超过64个字符', trigger: 'blur' }
        ]
      }

      // 只有在新增模式下才需要验证作业主体、客户、合同
      if (this.mode === 'new') {
        baseRules.supplierId = [
          { required: true, message: '请选择所属作业主体', trigger: 'change' }
        ]
        baseRules.customerId = [
          { required: true, message: '请选择所属客户', trigger: 'change' }
        ]
        baseRules.contractId = [
          { required: true, message: '请选择所属服务合同', trigger: 'change' }
        ]
      }

      return baseRules
    }
  },
  async created() {
    // 判断页面模式
    if (this.$route.params.id) {
      this.laborId = this.$route.params.id
      this.mode = 'view'
      await this.loadLaborData()
    } else {
      this.mode = 'new'
      await this.loadSupplierOptions()
      await this.loadCustomerOptions()
    }
  },

  methods: {
    // 获取页面标题
    getSectionTitle() {
      switch (this.mode) {
        case 'new':
          return '添加人员'
        case 'view':
          return '基本信息'
        case 'edit':
          return '基本信息'
        default:
          return '基本信息'
      }
    },

    // 切换编辑模式
    toggleEditMode() {
      this.mode = 'edit'
      // 保存当前数据状态
      this.originalFormData = JSON.parse(JSON.stringify(this.form))

      // 如果当前没有项目经历数据，添加一个空的项目经历输入框
      if (!this.form.laborProjectHistory || this.form.laborProjectHistory.length === 0 || !this.hasProjectData) {
        this.form.laborProjectHistory = [{
          projectName: '',
          post: '',
          projectTime: null,
          description: '',
          performance: ''
        }]
      }
    },

    // 取消编辑
    cancelEdit() {
      this.mode = 'view'
      // 恢复原始数据
      this.form = JSON.parse(JSON.stringify(this.originalFormData))
      // 清除表单验证状态
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },

    // 确认编辑
    async confirmEdit() {
      try {
        const valid = await this.$refs.form.validate()
        if (!valid) {
          return
        }

        this.submitting = true

        // 获取上传文件的ID数组
        let attachmentIds = []
        if (this.$refs.attachmentDisplayComponent) {
          attachmentIds = await this.$refs.attachmentDisplayComponent.getFileIds()
        }

        // 处理项目经历数据
        const processedProjectHistory = this.form.laborProjectHistory
          .filter(project =>
            project.projectName || project.post || project.description || project.performance
          )
          .map(project => {
            const result = {
              projectName: project.projectName || '',
              post: project.post || '',
              description: project.description || '',
              performance: project.performance || ''
            }

            // 处理项目时间
            if (project.projectTime && Array.isArray(project.projectTime)) {
              result.projectStart = project.projectTime[0] || ''
              result.projectEnd = project.projectTime[1] || ''
            } else {
              result.projectStart = ''
              result.projectEnd = ''
            }

            return result
          })

        // 构造更新数据，按照新的接口示例格式
        const updateData = {
          name: this.form.name,
          idCard: this.form.idCard,
          cellphone: this.form.cellPhone,
          signStatus: "",
          birthdayDate: "",
          age: parseInt(this.form.age) || 0,
          gender: this.form.gender,
          idCardPeriod: this.form.idCardPeriod,
          nation: this.form.nation,
          address: this.form.address,
          bankCardNo: this.form.bankCard || '',
          bankName: this.form.cardBank || '',
          id: parseInt(this.laborId),
          supplierName: "",
          corporationId: 0,
          corporationName: "",
          customerId: 0,
          customerName: "",
          contractId: 0,
          contractName: "",
          education: "",
          nativeField: "",
          householdRegistrationType: "",
          householdCity: "",
          householdAddress: "",
          maritalStatus: "",
          children: "",
          political: "",
          inWorkDay: "",
          deptId: 0,
          employed: 0,
          joinDate: "",
          post: "",
          empStatus: "",
          workEmail: "",
          wechat: "",
          personalWechat: "",
          workMobile: "",
          mobileNumber: "",
          personalEmail: "",
          bankCard: "",
          openCardCity: "",
          cardBank: "",
          bankBranch: "",
          createTime: "",
          modifyTime: "",
          laborProjectHistory: processedProjectHistory,
          attachments: attachmentIds
        }

        const [err, response] = await client.updateLabor({
          body: updateData
        })

        if (err) {
          handleError(err)
          return
        }

        if (response && response.success) {
          this.$message.success('人员信息更新成功')
          this.mode = 'view'
          // 重新加载数据
          await this.loadLaborData()
        } else {
          this.$message.error(response?.message || '更新失败')
        }
      } catch (error) {
        console.error('更新失败：', error)
        this.$message.error(error.message || '更新失败，请重试')
      } finally {
        this.submitting = false
      }
    },

    // 加载人员详情数据
    async loadLaborData() {
      try {
        this.pageLoading = true
        const [err, response] = await client.getLaborDetail({
          method: 'GET',
          pathParams: { id: this.laborId }
        })

        if (err) {
          handleError(err)
          return
        }

        if (response && response.success) {
          this.populateFormData(response.data)
        } else {
          this.$message.error(response?.message || '获取人员信息失败')
        }
      } catch (error) {
        handleError(error)
      } finally {
        this.pageLoading = false
      }
    },

    // 填充表单数据
    populateFormData(data) {
      // 基本信息
      const basicInfo = data.basicInfo || {}
      const laborBankCard = data.laborBankCard || {}

      this.form = {
        name: basicInfo.name || '',
        idCard: basicInfo.idCard || '',
        cellPhone: basicInfo.cellphone || '',
        age: basicInfo.age || '',
        gender: basicInfo.gender || '',
        idCardPeriod: basicInfo.idCardPeriod || '',
        nation: basicInfo.nation || '',
        address: basicInfo.address || '',
        bankCard: laborBankCard.bankCardNo || '',
        cardBank: laborBankCard.bankName || '',
        supplierId: '',
        customerId: '',
        contractId: '',
        laborProjectHistory: [],
        attachments: []
      }

      // 项目经历
      if (data.projectHistory && data.projectHistory.length > 0) {
        this.form.laborProjectHistory = data.projectHistory.map(project => ({
          projectName: project.projectName || '',
          post: project.post || '',
          projectTime: project.projectStart && project.projectEnd
            ? [project.projectStart, project.projectEnd]
            : null,
          description: project.description || '',
          performance: project.performance || ''
        }))
      } else {
        // 如果没有项目经历数据，设置为空数组
        this.form.laborProjectHistory = []
      }

      // 附件信息
      if (data.photos && data.photos.length > 0) {
        this.form.attachments = data.photos
      } else {
        this.form.attachments = []
      }

      // 保存原始数据用于取消编辑
      this.originalFormData = JSON.parse(JSON.stringify(this.form))
    },

    // 输入处理方法
    handlePhoneInput() {
      // 只允许输入数字
      this.form.cellPhone = this.form.cellPhone.replace(/[^\d]/g, '')
    },

    handleBankCardInput() {
      // 只允许输入数字
      this.form.bankCard = this.form.bankCard.replace(/[^\d]/g, '')
    },

    // 项目经历相关方法
    addProjectExperience() {
      // 如果当前没有项目经历数据，初始化数组
      if (!this.form.laborProjectHistory) {
        this.form.laborProjectHistory = []
      }
      this.form.laborProjectHistory.push({
        projectName: '',
        post: '',
        projectTime: null,
        description: '',
        performance: ''
      })
    },

    removeProjectExperience(index) {
      if (this.form.laborProjectHistory.length > 1) {
        this.form.laborProjectHistory.splice(index, 1)
      }
    },

    // 文件上传相关方法
    handleFilesChange(fileIds) {
      // 只接收文件ID数组，确保不会重复显示
      this.form.attachments = fileIds
    },

    // 加载作业主体选项
    async loadSupplierOptions() {
      try {
        const [err, response] = await client.listCorporation({
          body: { filters: {} }
        })

        if (response && response.success && response.data) {
          this.supplierOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载作业主体选项失败：', error)
      }
    },

    // 加载客户选项
    async loadCustomerOptions() {
      const conditions = {
        filters: {
          corporationIds: [],
        }
      }
      try {
        const [err, response] = await client.supplierListCustomer({
          body: { filters: conditions.filters }
        })

        if (response && response.success && response.data) {
          this.customerOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载客户选项失败：', error)
      }
    },

    // 加载合同选项
    async loadContractOptions() {
      if (!this.form.supplierId || !this.form.customerId) {
        this.contractOptions = []
        return
      }

      try {
        const [err, response] = await client.listContractByCustomerAndCorporation({
          body: { filters: {  
            customerId: this.form.customerId,
            supplierCorporationId: this.form.supplierId
          } }
        })

        if (response && response.success && response.data) {
          this.contractOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载合同选项失败：', error)
        this.contractOptions = []
      }
    },

    // 作业主体变化时
    onSupplierChange() {
      this.form.contractId = ''
      this.loadContractOptions()
    },

    // 客户变化时
    onCustomerChange() {
      this.form.contractId = ''
      this.loadContractOptions()
    },

    // 提交表单
    async onSubmit() {
      try {
        const valid = await this.$refs.form.validate()
        if (!valid) {
          this.$message.warning('请填写完整人员信息')
          return
        }
        
        this.submitting = true

      try {
        // 获取上传文件的ID数组
        let attachmentIds = []
        if (this.$refs.attachmentDisplayComponent) {
          attachmentIds = await this.$refs.attachmentDisplayComponent.getFileIds()
        }

        // 处理项目经历数据
        const processedProjectHistory = this.form.laborProjectHistory
          .filter(project =>
            project.projectName || project.post || project.description || project.performance
          )
          .map(project => {
            const result = {
              projectName: project.projectName || '',
              post: project.post || '',
              description: project.description || '',
              performance: project.performance || ''
            }

            // 处理项目时间
            if (project.projectTime && Array.isArray(project.projectTime)) {
              result.projectStart = project.projectTime[0] || ''
              result.projectEnd = project.projectTime[1] || ''
            } else {
              result.projectStart = ''
              result.projectEnd = ''
            }

            return result
          })

        // 根据新的接口报文格式构造数据
        const submitData = {
          name: this.form.name,
          idCard: this.form.idCard,
          cellphone: this.form.cellPhone,
          signStatus: "",
          birthdayDate: "",
          age: 0,
          gender: "",
          idCardPeriod: "",
          nation: "",
          address: "",
          bankCardNo: this.form.bankCard || '',
          bankName: this.form.cardBank || '',
          id: 0,
          supplierName: "",
          corporationId: this.form.supplierId,
          corporationName: "",
          customerId: this.form.customerId,
          customerName: "",
          contractId: this.form.contractId,
          contractName: "",
          education: "",
          nativeField: "",
          householdRegistrationType: "",
          householdCity: "",
          householdAddress: "",
          maritalStatus: "",
          children: "",
          political: "",
          inWorkDay: "",
          deptId: 0,
          employed: 0,
          joinDate: "",
          post: "",
          empStatus: "",
          workEmail: "",
          wechat: "",
          personalWechat: "",
          workMobile: "",
          mobileNumber: "",
          personalEmail: "",
          bankCard: "",
          openCardCity: "",
          cardBank: "",
          bankBranch: "",
          createTime: "",
          modifyTime: "",
          laborProjectHistory: processedProjectHistory,
          attachments: attachmentIds
        }

        const [err, response] = await client.addLabor({
          body: submitData
        })

        if (err) {
          handleError(err)
          return
        }

        if (response && response.success) {
          this.$message.success('人员添加成功')
          this.$router.push('/supplierLabor')
        } else {
          this.$message.error(response?.message || '添加失败')
        }
      } catch (error) {
        console.error('提交失败：', error)
        this.$message.error(error.message || '提交失败，请重试')
      } finally {
        this.submitting = false
      }
    } catch (validationError) {
      console.error('表单验证错误：', validationError)
      this.$message.warning('请填写完整人员信息')
    }
    },

    // 取消
    onCancel() {
      this.$router.back()
    }
  }
}
</script>

<style scoped>
.labor-new-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  box-sizing: border-box;
}

.edit-button-container {
  background: white;
  padding: 10px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
  max-width: 1200px;
}

.edit-button {
  background: #4F71FF;
  border-color: #4F71FF;
}

.empty-tip {
  color: #909399;
  font-size: 14px;
  text-align: center;
  padding: 40px 0;
  background-color: #fafafa;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  margin: 20px 0;
}

.form-container {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  width: 100%;
  max-width: 1200px;
  min-height: 500px;
}

.labor-form {
  width: 100%;
}

.form-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 24px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
  display: inline-block;
}

.el-form-item {
  margin-bottom: 24px;
}

.el-form-item__label {
  font-size: 14px;
  color: #606266;
  font-weight: 550;
  line-height: 32px;
}

.el-input,
.el-select {
  width: 100%;
}

.form-actions {
  text-align: center;
  padding-top: 32px;
  border-top: 1px solid #ebeef5;
  margin-top: 40px;
}

.form-actions .el-button {
  margin: 0 12px;
  padding: 12px 32px;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.3s;
}

.form-actions .el-button--primary {
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
}

.form-actions .el-button--primary:hover {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  transform: translateY(-1px);
}

.form-actions .el-button:not(.el-button--primary) {
  color: #606266;
  border-color: #dcdfe6;
  background-color: #fff;
}

.form-actions .el-button:not(.el-button--primary):hover {
  color: #409EFF;
  border-color: #409EFF;
  background-color: #ecf5ff;
  transform: translateY(-1px);
}

/* 项目经历样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
  display: inline-block;
}

.add-project-btn {
  color: #409EFF !important;
  padding: 4px 8px !important;
  margin: 0 !important;
  font-size: 14px;
}

.project-item >>> .el-textarea__inner {
  min-height: 60px !important;
  height: 60px !important;
  resize: none;
}

.add-project-btn:hover {
  background-color: #ecf5ff !important;
}

.add-icon {
  font-size: 12px;
  font-weight: bold;
  margin-right: 4px;
  border: 1px solid #409EFF;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.project-item {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 20px 60px 0px 20px;
  margin-bottom: 20px;
  position: relative;
}

.project-item:last-child {
  margin-bottom: 0;
}

.project-header {
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 10;
}

.delete-project-btn {
  color: #f56c6c !important;
  padding: 4px !important;
  margin: 0 !important;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-project-btn:hover {
  background-color: rgba(245, 108, 108, 0.2) !important;
  color: #f78989 !important;
}

.delete-project-btn .el-icon-close {
  font-size: 12px;
}

/* 附件上传样式 */
.upload-section {
  margin-top: 16px;
}

.upload-tip {
  color: #909399;
  font-size: 12px;
  margin-bottom: 12px;
  line-height: 1.5;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .labor-new-container {
    padding: 20px 12px;
  }

  .form-container {
    padding: 24px;
  }

  .el-form-item__label {
    width: 100px !important;
  }
}
</style>
