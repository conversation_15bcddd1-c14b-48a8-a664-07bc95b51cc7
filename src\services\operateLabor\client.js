class Client {
  constructor(httpClient) {
    if (!httpClient) {
      throw new Error('httpClient is required')
    }

    this.httpClient = httpClient
  }
  // 当前登录用户信息
  async supplierProfile(options = {}) {
    const resource = `/api/supplier/profile`
    return this.httpClient.request(resource, options)
  }
  //当前登录用户信息
  async customerProfile(options = {}) {
    const resource = `/api/customer/profile`
    return this.httpClient.request(resource, options)
  }
  //当前登录用户信息
  async personalLaborProfile(options = {}) {
    const resource = `/api/personal/labor/profile`
    return this.httpClient.request(resource, options)
  }

  //获取用户作业主体列表
  async listAvailableCorporation(options = {}) {
    const resource = `/api/public/listAvailableCorporation`
    return this.httpClient.request(resource, options)
  }

  //获取用户作业主体列表
  async apiProxyListProxy(options = {}) {
    const resource = `/api/personal/proxy/listProxy`
    return this.httpClient.request(resource, options)
  }

  //小程序-人员合同列表
  async apiLaborLaborContractList(options = {}) {
    const resource = `/api/personal/labor/laborContractList`
    return this.httpClient.request(resource, options)
  }

  //小程序-人员详情
  async apiLaborLaborDetail(laborInfoId, options = {}) {
    const resource = `/api/personal/labor/laborDetail/${laborInfoId}`
    return this.httpClient.request(resource, options)
  }

  //角色列表
  async listRoles(options = {}) {
    const resource = `/api/supplier/listRole`
    return this.httpClient.request(resource, options)
  }
  // 图形验证码
  async createCaptcha(options = {}) {
    const resource = `/api/public/createCaptcha`
    return this.httpClient.request(resource, options)
  }

  // 发送登录验证码
  async apiSendLoginSms(options = {}) {
    const resource = `/api/public/sendLoginSms`
    return this.httpClient.request(resource, options)
  }

  // 发送短信验证码
  async sendOtp(options = {}) {
    const resource = `/api/public/sendOtp`
    return this.httpClient.request(resource, options)
  }

  // 发送确认支付验证码
  async proxySendOtp(options = {}) {
    const resource = `/api/supplier/proxy/sendOtp`
    return this.httpClient.request(resource, options)
  }
  // 登录
  async login(options = {}) {
    const resource = `/api/public/login`
    return this.httpClient.request(resource, options)
  }
  // 域名获取品牌信息
  async domainInfo(options = {}) {
    const resource = `/api/public/domainInfo`
    return this.httpClient.request(resource, options)
  }
  // 客户列表
  async supplierListCustomer(options = {}) {
    const resource = `/api/supplier/customer/listCustomer`
    return this.httpClient.request(resource, options)
  }

  // 查询平台下客户列表
  async queryCustomerBySupplier(options = {}) {
    const resource = `/api/supplier/customer/queryCustomerBySupplier`
    return this.httpClient.request(resource, options)
  }

  // 新增客户
  async addCustomer(options = {}) {
    const resource = `/api/supplier/customer/addCustomer`
    return this.httpClient.request(resource, options)
  }

  // 查询客户详情
  async queryCustomer(options = {}) {
    const resource = `/api/supplier/customer/queryCustomer`
    return this.httpClient.request(resource, options)
  }

  // 更新客户信息
  async updateCustomer(options = {}) {
    const resource = `/api/supplier/customer/updateCustomer`
    return this.httpClient.request(resource, options)
  }

  // 更新客户状态
  async updateCustomerStatus(options = {}) {
    const resource = `/api/supplier/customer/updateCustomerStatus`
    return this.httpClient.request(resource, options)
  }

  // 菜单
  async supplierGetMenu(options = {}) {
    const resource = `/api/supplier/getMenu`
    return this.httpClient.request(resource, options)
  }
  // 商户菜单
  async merchantGetMenu(options = {}) {
    const resource = `/api/customer/getMenu`
    return this.httpClient.request(resource, options)
  }
  // 获取可用客户列表
  async listAvailableCustomer(options = {}) {
    const resource = `/api/public/listAvailableCustomer`
    return this.httpClient.request(resource, options)
  }
  // 续期token
  async renewToken(options = {}) {
    const resource = `/api/public/renewToken`
    return this.httpClient.request(resource, options)
  }

  // 权限树
  async supplierGetAuthorityTree(options = {}) {
    const resource = `/api/supplier/getAuthorityTree`
    return this.httpClient.request(resource, options)
  }

  // 客户权限树
  async customerGetAuthorityTree(options = {}) {
    const resource = `/api/customer/getAuthorityTree`
    return this.httpClient.request(resource, options)
  }

  // 创建权限
  async createRole(options = {}) {
    const resource = `/api/supplier/addRole`
    return this.httpClient.request(resource, options)
  }
  // 启用/禁用角色
  async disableRole(options = {}) {
    const resource = `/api/supplier/disableRole`
    return this.httpClient.request(resource, options)
  }
  // 删除角色
  async deleteRole(options = {}) {
    const resource = `/api/supplier/deleteRole`
    return this.httpClient.request(resource, options)
  }
  // 编辑角色
  async editRole(options = {}) {
    const resource = `/api/supplier/editRole`
    return this.httpClient.request(resource, options)
  }
  // 角色详情
  async roleDetail(options = {}) {
    const resource = `/api/supplier/roleDetail`
    return this.httpClient.request(resource, options)
  }
  // 获取角色成员
  async getRoleMembers(options = {}) {
    const resource = `/api/supplier/getRoleMembers`
    return this.httpClient.request(resource, options)
  }

  // 客户创建角色
  async customerAddRole(options = {}) {
    const resource = `/api/customer/addRole`
    return this.httpClient.request(resource, options)
  }
  // 客户启用/禁用角色
  async customerDisableRole(options = {}) {
    const resource = `/api/customer/disableRole`
    return this.httpClient.request(resource, options)
  }
  // 客户编辑角色
  async customerEditRole(options = {}) {
    const resource = `/api/customer/editRole`
    return this.httpClient.request(resource, options)
  }
  // 客户角色详情
  async customerRoleDetail(options = {}) {
    const resource = `/api/customer/roleDetail`
    return this.httpClient.request(resource, options)
  }
  // 客户获取角色成员
  async customerGetRoleMembers(options = {}) {
    const resource = `/api/customer/getRoleMembers`
    return this.httpClient.request(resource, options)
  }
  // 客户角色列表
  async customerGetRoles(options = {}) {
    const resource = `/api/customer/listRole`
    return this.httpClient.request(resource, options)
  }

  // 服务合同列表
  async supplierListContract(options = {}) {
    const resource = `/api/supplier/contract/listContract`
    return this.httpClient.request(resource, options)
  }

  // 新增服务合同
  async addContract(options = {}) {
    const resource = `/api/supplier/contract/addContract`
    return this.httpClient.request(resource, options)
  }

  // 查询服务合同详情
  async queryContract(options = {}) {
    const resource = `/api/supplier/contract/queryContract`
    return this.httpClient.request(resource, options)
  }

  // 更新服务合同
  async updateContract(options = {}) {
    const resource = `/api/supplier/contract/updateContract`
    return this.httpClient.request(resource, options)
  }

  // 提前终止服务合同
  async terminateContract(options = {}) {
    const resource = `/api/supplier/contract/terminateContract`
    return this.httpClient.request(resource, options)
  }

  // 客户端服务合同列表
  async customerListContract(options = {}) {
    const resource = `/api/customer/contract/listContract`
    return this.httpClient.request(resource, options)
  }

  // 客户端查询服务合同详情
  async customerQueryContract(options = {}) {
    const resource = `/api/customer/contract/queryContract`
    return this.httpClient.request(resource, options)
  }

  // 客户端获取所有合同
  async customerGetAllContracts(options = {}) {
    const resource = `/api/customer/contract/allContractByCustomer`
    return this.httpClient.request(resource, options)
  }

  // 客户端获取作业主体列表
  async customerGetSuppliers(options = {}) {
    const resource = `/api/customer/supplier/list`
    return this.httpClient.request(resource, options)
  }

  // 客户端获取人员列表
  async customerGetLabors(options = {}) {
    const resource = `/api/customer/labor/list`
    return this.httpClient.request(resource, options)
  }

  // 客户端人员离职
  async customerResignLabor(options = {}) {
    const resource = `/api/customer/labor/resign`
    return this.httpClient.request(resource, options)
  }

  // 客户端获取人员详情
  async customerGetLaborDetail(options = {}) {
    const resource = `/api/customer/labor/detail`
    return this.httpClient.request(resource, options)
  }

  // 客户端更新人员详情
  async customerUpdateLaborDetail(options = {}) {
    const resource = `/api/customer/labor/update`
    return this.httpClient.request(resource, options)
  }

  // 客户端添加人员
  async customerAddLabor(options = {}) {
    const resource = `/api/customer/labor/add`
    return this.httpClient.request(resource, options)
  }

  // 客户端获取客户列表
  async customerGetCustomers(options = {}) {
    const resource = `/api/customer/customer/list`
    return this.httpClient.request(resource, options)
  }

  // 客户端根据作业主体和客户获取合同
  async customerGetContractsBySupplierAndCustomer(options = {}) {
    const resource = `/api/customer/contract/listBySupplierAndCustomer`
    return this.httpClient.request(resource, options)
  }

  // 客户端获取工资表列表
  async customerGetPayrolls(options = {}) {
    const resource = `/api/customer/payroll/list`
    return this.httpClient.request(resource, options)
  }

  // 客户端获取工资表详情
  async customerGetPayrollDetail(options = {}) {
    const resource = `/api/customer/payroll/detail`
    return this.httpClient.request(resource, options)
  }

  // 客户端新增工资表
  async customerAddPayroll(options = {}) {
    const resource = `/api/customer/payroll/add`
    return this.httpClient.request(resource, options)
  }

  // 客户端下载工资表模板
  async customerDownloadPayrollTemplate(options = {}) {
    const resource = `/api/customer/payroll/template`
    return this.httpClient.request(resource, options)
  }

  // ========== Merchant独立的薪酬管理API ==========

  // 薪酬管理-新增工资表
  async customerSalaryAddPayroll(options = {}) {
    const resource = `/api/customer/salary/addPayroll`
    return this.httpClient.request(resource, {
      ...options,
      requestInterceptor(resource, options) {
        delete options.headers['Content-Type']
        return [null, resource, options]
      }
    })
  }

  // 薪酬计算-列表
  async customerSalaryListPayroll(options = {}) {
    const resource = `/api/customer/salary/listPayroll`
    return this.httpClient.request(resource, options)
  }

  // 薪酬计算-查看
  async customerSalaryListPayrollDetail(options = {}) {
    const resource = `/api/customer/salary/listPayrollDetail`
    return this.httpClient.request(resource, options)
  }

  // 应发模板下载
  async customerSalaryDownloadTemplate(options = {}) {
    const resource = `/api/customer/salary/download/template`
    return this.httpClient.request(resource, options)
  }

  // 错误日志下载
  async customerSalaryImportVerifyErrorLog(uuid, options = {}) {
    const resource = `/api/customer/salary/importVerifyErrorLog/${uuid}`
    return this.httpClient.request(resource, options)
  }

  // 发薪超10w人员统计
  async customerSalarySalaryOverStaffCount(options = {}) {
    const resource = `/api/customer/salary/salaryOverStaffCount`
    return this.httpClient.request(resource, options)
  }

  // 薪酬计算-下载
  async customerSalaryPayRollDetailDownload(options = {}) {
    const resource = `/api/customer/salary/payRollDetailDownload`
    return this.httpClient.request(resource, options)
  }

  // 薪酬计算-删除工资表
  async customerSalaryDeletePayroll(options = {}) {
    const resource = `/api/customer/salary/deletePayroll`
    return this.httpClient.request(resource, options)
  }

  // 客户端获取账单列表
  async customerGetBills(options = {}) {
    const resource = `/api/customer/bill/list`
    return this.httpClient.request(resource, options)
  }

  // 客户端获取账单详情
  async customerGetBillDetail(options = {}) {
    const resource = `/api/customer/bill/detail`
    return this.httpClient.request(resource, options)
  }

  // 客户端确认账单
  async customerConfirmBill(options = {}) {
    const resource = `/api/customer/bill/confirm`
    return this.httpClient.request(resource, options)
  }

  // 分页查询账单列表 (新接口)
  async apiCustomerBillsList(options = {}) {
    const resource = `/api/customer/bills/list`
    return this.httpClient.request(resource, options)
  }

  // 获取账单详情 (新接口)
  async apiCustomerBillsDetail(options = {}) {
    const resource = `/api/customer/bills/detail`
    return this.httpClient.request(resource, options)
  }

  // 确认账单 (新接口)
  async apiCustomerBillsConfirm(options = {}) {
    const resource = `/api/customer/bills/confirm`
    return this.httpClient.request(resource, options)
  }

  // 获取账单薪酬明细 (新接口)
  async apiCustomerBillsSalaryDetails(options = {}) {
    const resource = `/api/customer/bills/salary-details`
    return this.httpClient.request(resource, options)
  }

  // 获取账单其他费用明细 (新接口)
  async apiCustomerBillsOtherFeeDetails(options = {}) {
    const resource = `/api/customer/bills/other-fee-details`
    return this.httpClient.request(resource, options)
  }

  // 获取账单管理费明细 (新接口)
  async apiCustomerBillsManagementFeeDetails(options = {}) {
    const resource = `/api/customer/bills/management-fee-details`
    return this.httpClient.request(resource, options)
  }

  // 客户端获取发票列表
  async customerGetInvoices(options = {}) {
    const resource = `/api/customer/invoice/list`
    return this.httpClient.request(resource, options)
  }

  // 客户端获取发票详情
  async customerGetInvoiceDetail(options = {}) {
    const resource = `/api/customer/invoice/detail`
    return this.httpClient.request(resource, options)
  }

  // 上传文件
  async uploadFile(options = {}) {
    const resource = `/api/public/uploadFile`
    return this.httpClient.request(resource, {
      ...options,
      requestInterceptor(resource, options) {
        delete options.headers['Content-Type']
        return [null, resource, options]
      }
    })
  }

  // 业务主体列表
  async listCorporation(options = {}) {
    const resource = `/api/supplier/listCorporation`
    return this.httpClient.request(resource, options)
  }

  // 作业主体详情
  async corporationDetail(options = {}) {
    const resource = `/api/supplier/corporationDetail`
    return this.httpClient.request(resource, options)
  }
  //  作业主体配置详情
  async corporationConfigDetail(options = {}) {
    const resource = `/api/supplier/corporationConfigDetail`
    return this.httpClient.request(resource, options)
  }

  // 添加作业主体
  async addCorporation(options = {}) {
    const resource = `/api/supplier/addCorporation`
    return this.httpClient.request(resource, options)
  }

  // 修改作业主体
  async editCorporation(options = {}) {
    const resource = `/api/supplier/editCorporation`
    return this.httpClient.request(resource, options)
  }

  // 添加修改作业主体配置
  async editCorporationBusiness(options = {}) {
    const resource = `/api/supplier/editCorporationBusiness`
    return this.httpClient.request(resource, options)
  }
  // 获取可用通道
  async supplierPayChannelList(options = {}) {
    const resource = `/api/supplier/supplierPayChannelList`
    return this.httpClient.request(resource, options)
  }
  // 配置通道参数
  async supplierCorporationConfigPayChannel(options = {}) {
    const resource = `/api/supplier/corporationConfigPayChannel`
    return this.httpClient.request(resource, options)
  }

  // 首次设置密码
  async setPassword(options = {}) {
    const resource = `/api/supplier/setPassword`
    return this.httpClient.request(resource, options)
  }

  async customerSetPassword (options = {}) {
    const resource = `/api/customer/setPassword`
    return this.httpClient.request(resource, options)
  }

  // 重置密码
  async resetPassword(options = {}) {
    const resource = `/api/supplier/resetPassword`
    return this.httpClient.request(resource, options)
  }

  async customerResetPassword(options = {}) {
    const resource = `/api/customer/resetPassword`
    return this.httpClient.request(resource, options)
  }

  // 获取成员
  async supplierGetMembers(options = {}) {
    const resource = `/api/supplier/getMembers`
    return this.httpClient.request(resource, options)
  }

  // 启用/禁用成员
  async supplierDisableMember(options = {}) {
    const resource = `/api/supplier/disableMember`
    return this.httpClient.request(resource, options)
  }

  // 删除成员
  async supplierRemoveMember(options = {}) {
    const resource = `/api/supplier/removeMember`
    return this.httpClient.request(resource, options)
  }

  // 添加成员
  async supplierAddMember(options = {}) {
    const resource = `/api/supplier/addMember`
    return this.httpClient.request(resource, options)
  }

  // 编辑成员
  async supplierEditMember(options = {}) {
    const resource = `/api/supplier/editMember`
    return this.httpClient.request(resource, options)
  }

  // 客户获取成员
  async customerGetMembers(options = {}) {
    const resource = `/api/customer/getMembers`
    return this.httpClient.request(resource, options)
  }

  // 客户启用/禁用成员
  async customerDisableMember(options = {}) {
    const resource = `/api/customer/disableMember`
    return this.httpClient.request(resource, options)
  }

  // 客户删除成员
  async customerRemoveMember(options = {}) {
    const resource = `/api/customer/removeMember`
    return this.httpClient.request(resource, options)
  }

  // 客户添加成员
  async customerAddMember(options = {}) {
    const resource = `/api/customer/addMember`
    return this.httpClient.request(resource, options)
  }

  // 客户编辑成员
  async customerEditMember(options = {}) {
    const resource = `/api/customer/editMember`
    return this.httpClient.request(resource, options)
  }

  // 获取供应商详情
  async supplierDetail(options = {}) {
    const resource = `/api/supplier/supplierDetail`
    return this.httpClient.request(resource, options)
  }

  // 编辑供应商信息
  async editSupplier(options = {}) {
    const resource = `/api/supplier/editSupplier`
    return this.httpClient.request(resource, options)
  }

  // 获取合同模板列表
  async getTemplateList(options = {}) {
    const resource = `/api/supplier/protocol/template/list`
    return this.httpClient.request(resource, options)
  }

  // 检查模板是否可以更新
  async canUpdateTemplate(id, options = {}) {
    options.method = 'GET'
    const resource = `/api/supplier/protocol/template/canUpdate?id=${id}`
    return this.httpClient.request(resource, options)
  }

  // 更新模板状态
  async updateTemplateStatus(options = {}) {
    const resource = `/api/supplier/protocol/template/updateTempStatus`
    return this.httpClient.request(resource, options)
  }

  // 获取模板详情
  async getTemplateDetail(id, options = {}) {
    const resource = `/api/supplier/protocol/template/get?id=${id}`
    return this.httpClient.request(resource, options)
  }

  // 创建/更新模板
  async createTemplate(options = {}) {
    const resource = `/api/supplier/protocol/template/create`
    return this.httpClient.request(resource, options)
  }
  //创建模板第二步
  async setTemplate(options = {}) {
    const resource = `/api/supplier/protocol/template/setTemplate`
    return this.httpClient.request(resource, options)
  }

  // 获取模板详情
  async getTemplateDetail(tempId, options = {}) {
    const resource = `/api/supplier/protocol/template/getTemplateDetail?tempId=${tempId}`
    return this.httpClient.request(resource, options)
  }

  async supplierLaborList(options = {}) {
    const resource = `/api/supplier/labor/list`
    return this.httpClient.request(resource, options)
  }

  // 根据客户和作业主体获取合同列表
  async listContractByCustomerAndCorporation(options = {}) {
    const resource = `/api/supplier/contract/listContractByCustomerAndCorporation`
    return this.httpClient.request(resource, options)
  }

  // 添加人员
  async addLabor(options = {}) {
    const resource = `/api/supplier/labor/create`
    return this.httpClient.request(resource, options)
  }

  // 获取模板字段
  async getTemplateFields(options = {}) {
    const resource = `/api/protocol/template/fields`
    return this.httpClient.request(resource, options)
  }

  // 提交模板配置
  async commitTemplate(options = {}) {
    const resource = `/api/protocol/template/commit`
    return this.httpClient.request(resource, options)
  }

  // 获取人员详情
  async getLaborDetail(options = {}) {
    const resource = `/api/supplier/labor/getDetail/${options.pathParams.id}`
    return this.httpClient.request(resource, options)
  }

  // 更新人员信息
  async updateLabor(options = {}) {
    const resource = `/api/supplier/labor/update`
    return this.httpClient.request(resource, options)
  }

  // 下载人员导入模板
  async downloadLaborTemplate(options = {}) {
    const resource = `/api/supplier/labor/download/template`
    return this.httpClient.request(resource, {
      ...options,
      headers: { 'content-type': 'application/octet-stream' }
    })
  }

  // 批量上传人员信息校验
  async batchUploadLaborCheck(options = {}) {
    const resource = `/api/supplier/labor/download/check`
    return this.httpClient.request(resource, {
      ...options,
      requestInterceptor(resource, options) {
        delete options.headers['Content-Type']
        return [null, resource, options]
      }
    })
  }

  // 导出错误数据
  async exportLaborErrorLog(uuid, options = {}) {
    const resource = `/api/supplier/labor/importVerifyErrorLog/${uuid}`
    return this.httpClient.request(resource, {
      ...options,
      method: 'GET',
      headers: { 'content-type': 'application/octet-stream' }
    })
  }
  // 根据域名获取配置信息
  async getDomainInfo(options = {}) {
    const resource = `/api/public/domainInfo`
    return this.httpClient.request(resource, options)
  }

  // 个税申报列表
  async personalTaxList(options = {}) {
    const resource = `/api/supplier/personaltax/list`
    return this.httpClient.request(resource, options)
  }

  // 生成个税申报表
  async addPersonalTax(options = {}) {
    const resource = `/api/supplier/personaltax/add`
    return this.httpClient.request(resource, options)
  }

  // 查询个税申报详情
  async queryPersonalTax(options = {}) {
    const resource = `/api/supplier/personaltax/query`
    return this.httpClient.request(resource, options)
  }

  // 增值税申报列表
  async valueAddedTaxList(options = {}) {
    const resource = `/api/supplier/valueaddedtax/list`
    return this.httpClient.request(resource, options)
  }

  // 生成增值税申报表
  async addValueAddedTax(options = {}) {
    const resource = `/api/supplier/valueaddedtax/add`
    return this.httpClient.request(resource, options)
  }

  // 查询增值税申报详情
  async queryValueAddedTax(options = {}) {
    const resource = `/api/supplier/valueaddedtax/query`
    return this.httpClient.request(resource, options)
  }

  // 更新个税申报状态
  async updatePersonalTaxStatus(options = {}) {
    const resource = `/api/supplier/personaltax/updateTaxStatus`
    return this.httpClient.request(resource, options)
  }

  // 更新增值税申报状态
  async updateValueAddedTaxStatus(options = {}) {
    const resource = `/api/supplier/valueaddedtax/updateTaxStatus`
    return this.httpClient.request(resource, options)
  }

  // 税款缴纳凭证列表
  async taxPaymentVoucherList(options = {}) {
    const resource = `/api/supplier/taxpaymentvoucher/list`
    return this.httpClient.request(resource, options)
  }

  // 新增税款缴纳凭证
  async addTaxPaymentVoucher(options = {}) {
    const resource = `/api/supplier/taxpaymentvoucher/add`
    return this.httpClient.request(resource, options)
  }

  // 查询税款缴纳凭证详情
  async queryTaxPaymentVoucher(options = {}) {
    const resource = `/api/supplier/taxpaymentvoucher/query`
    return this.httpClient.request(resource, options)
  }

  // 获取文件信息
  async describeFile(options = {}) {
    const resource = `/api/public/describeFile`
    return this.httpClient.request(resource, options)
  }

  // 人员信息报送列表
  async infoLaborList(options = {}) {
    const resource = `/api/supplier/infolabor/list`
    return this.httpClient.request(resource, options)
  }

  // 生成人员信息报送表
  async addInfoLabor(options = {}) {
    const resource = `/api/supplier/infolabor/add`
    return this.httpClient.request(resource, options)
  }

  // 下载人员信息报送表
  async downloadInfoLabor(options = {}) {
    const resource = `/api/supplier/infolabor/download`
    return this.httpClient.request(resource, options)
  }

  // 人员收入信息报送列表
  async infoIncomeList(options = {}) {
    const resource = `/api/supplier/infoincome/list`
    return this.httpClient.request(resource, options)
  }

  // 生成人员收入信息报送表
  async addInfoIncome(options = {}) {
    const resource = `/api/supplier/infoincome/add`
    return this.httpClient.request(resource, options)
  }

  // 下载人员收入信息报送表
  async downloadInfoIncome(options = {}) {
    const resource = `/api/supplier/infoincome/download`
    return this.httpClient.request(resource, options)
  }

  // 企业信息报送列表
  async infoEnterpriseList(options = {}) {
    const resource = `/api/supplier/infoenterprise/list`
    return this.httpClient.request(resource, options)
  }

  // 生成企业信息报送表
  async addInfoEnterprise(options = {}) {
    const resource = `/api/supplier/infoenterprise/add`
    return this.httpClient.request(resource, options)
  }

  // 下载企业信息报送表
  async downloadInfoEnterprise(options = {}) {
    const resource = `/api/supplier/infoenterprise/download`
    return this.httpClient.request(resource, options)
  }

  // 分页查询账单列表
  async apiSupplierBillsList(options = {}) {
    const resource = `/api/supplier/bills/list`
    return this.httpClient.request(resource, options)
  }

  // 生成账单
  async apiSupplierBillsGenerate(options = {}) {
    const resource = `/api/supplier/bills/generate`
    return this.httpClient.request(resource, options)
  }

  // 任务相关接口
  // 查询任务列表
  async supplierQueryTask(options = {}) {
    const resource = `/api/supplier/task/queryTask`
    return this.httpClient.request(resource, options)
  }

  // 创建任务
  async supplierAddTask(options = {}) {
    const resource = `/api/supplier/task/addTask`
    return this.httpClient.request(resource, options)
  }

  // 删除任务
  async supplierDeleteTask(options = {}) {
    const resource = `/api/supplier/task/deleteTask`
    return this.httpClient.request(resource, options)
  }

  // 获取任务详情
  async supplierGetTaskDetail(options = {}) {
    const resource = `/api/supplier/task/getTaskDetail`
    return this.httpClient.request(resource, options)
  }

  // 更新任务
  async supplierUpdateTask(options = {}) {
    const resource = `/api/supplier/task/updateTask`
    return this.httpClient.request(resource, options)
  }

  // 客户端任务相关接口
  // 客户端查询任务列表
  async customerQueryTask(options = {}) {
    const resource = `/api/customer/task/queryTask`
    return this.httpClient.request(resource, options)
  }

  // 客户端创建任务
  async customerAddTask(options = {}) {
    const resource = `/api/customer/task/addTask`
    return this.httpClient.request(resource, options)
  }

  // 客户端删除任务
  async customerDeleteTask(options = {}) {
    const resource = `/api/customer/task/deleteTask`
    return this.httpClient.request(resource, options)
  }

  // 客户端获取任务详情
  async customerGetTaskDetail(options = {}) {
    const resource = `/api/customer/task/getTaskDetail`
    return this.httpClient.request(resource, options)
  }

  // 客户端更新任务
  async customerUpdateTask(options = {}) {
    const resource = `/api/customer/task/updateTask`
    return this.httpClient.request(resource, options)
  }

  // 查询任务标签
  async queryTaskTag(options = {}) {
    const resource = `/api/supplier/task/queryTaskTag`
    return this.httpClient.request(resource, options)
  }

  // 查询任务标签下拉列表
  async queryPublicTaskTag(options = {}) {
    const resource = `/api/public/task/queryTaskTag`
    return this.httpClient.request(resource,  { ...options, method: 'GET' })
  }

  // 新增任务标签
  async addTaskTag(options = {}) {
    const resource = `/api/supplier/task/addTaskTag`
    return this.httpClient.request(resource, options)
  }

  // 删除任务标签
  async deleteTaskTag(options = {}) {
    const resource = `/api/supplier/task/removeTaskTag`
    return this.httpClient.request(resource, options)
  }

  // 获取合同模板列表
  async listCorporationTemp(options = {}) {
    const resource = `/api/public/protocol/template/listCorporationTemp`
    return this.httpClient.request(resource, options)
  }

  // 获取任务详情
  async getTaskDetail(taskId, options = {}) {
    const resource = `/api/customer/task/get/${taskId}`
    return this.httpClient.request(resource, { ...options, method: 'GET' })
  }

  // 查询任务人员列表
  async customerQueryTaskLabor(options = {}) {
    const resource = `/api/customer/task/queryTaskLabor`
    return this.httpClient.request(resource, options)
  }

  // 下载指派人员模板
  async downloadTaskLaborTemplate(options = {}) {
    const resource = `/api/public/task/download/template`
    return this.httpClient.request(resource, {
      ...options,
      method: 'GET',
      headers: { 'content-type': 'application/octet-stream' }
    })
  }

  // 批量导入指派人员
  async importTaskLabor(options = {}) {
    const resource = `/api/public/task/importTaskLabor`
    return this.httpClient.request(resource, options)
  }

  // 审核任务人员
  async auditTaskLabor(options = {}) {
    const resource = `/api/customer/task/auditTaskLabor`
    return this.httpClient.request(resource, options)
  }

  // 完成任务人员
  async finishTaskLabor(options = {}) {
    const resource = `/api/customer/task/finishTaskLabor`
    return this.httpClient.request(resource, options)
  }

  // 完成任务
  async customerFinishTask(options = {}) {
    const resource = `/api/customer/task/finishTask`
    return this.httpClient.request(resource, options)
  }

  // 停用任务
  async customerDisableTask(options = {}) {
    const resource = `/api/customer/task/disableTask`
    return this.httpClient.request(resource, options)
  }

  // 获取任务完成凭证
  async customerGetTaskCredentials(taskId, options = {}) {
    const resource = `/api/customer/task/taskCredentials/${taskId}`
    return this.httpClient.request(resource, { ...options, method: 'GET' })
  }

  // Platform (Supplier) 任务相关接口
  // 查询任务标签 - Platform
  async supplierQueryTaskTag(options = {}) {
    const resource = `/api/supplier/task/queryTaskTag`
    return this.httpClient.request(resource, options)
  }

  // 新增任务标签 - Platform
  async supplierAddTaskTag(options = {}) {
    const resource = `/api/supplier/task/addTaskTag`
    return this.httpClient.request(resource, options)
  }

  // 查询任务人员列表 - Platform
  async supplierQueryTaskLabor(options = {}) {
    const resource = `/api/supplier/task/queryTaskLabor`
    return this.httpClient.request(resource, options)
  }

  // 停用任务 - Platform
  async supplierDisableTask(options = {}) {
    const resource = `/api/supplier/task/disableTask`
    return this.httpClient.request(resource, options)
  }

  // 审核任务 - Platform
  async supplierAuditTask(options = {}) {
    const resource = `/api/supplier/task/auditTask`
    return this.httpClient.request(resource, options)
  }

  // 获取任务详情 - Platform
  async supplierGetTaskDetail(taskId, options = {}) {
    const resource = `/api/supplier/task/get/${taskId}`
    return this.httpClient.request(resource, { ...options, method: 'GET' })
  }

  // 完成任务 - Platform
  async supplierFinishTask(options = {}) {
    const resource = `/api/supplier/task/finishTask`
    return this.httpClient.request(resource, options)
  }

  // 获取任务完成凭证 - Platform
  async supplierGetTaskCredentials(taskId, options = {}) {
    const resource = `/api/supplier/task/taskCredentials/${taskId}`
    return this.httpClient.request(resource, { ...options, method: 'GET' })
  }

  // 查询任务列表 - Platform
  async supplierQueryTask(options = {}) {
    const resource = `/api/supplier/task/queryTask`
    return this.httpClient.request(resource, options)
  }

  // 添加任务 - Platform
  async supplierAddTask(options = {}) {
    const resource = `/api/supplier/task/addTask`
    return this.httpClient.request(resource, options)
  }

  // 获取所有合同 - Platform
  async supplierGetAllContracts(options = {}) {
    const resource = `/api/supplier/contract/getAllContracts`
    return this.httpClient.request(resource, options)
  }

  // 审核任务人员 - Platform
  async supplierAuditTaskLabor(options = {}) {
    const resource = `/api/supplier/task/auditTaskLabor`
    return this.httpClient.request(resource, options)
  }

  // 完成人员任务 - Platform
  async supplierFinishTaskLabor(options = {}) {
    const resource = `/api/supplier/task/finishTaskLabor`
    return this.httpClient.request(resource, options)
  }


  // 获取账单详情
  async apiSupplierBillsDetail(options = {}) {
    const resource = `/api/supplier/bills/detail`
    return this.httpClient.request(resource, options)
  }

  // 提交账单确认
  async apiSupplierBillsSubmit(options = {}) {
    const resource = `/api/supplier/bills/submit`
    return this.httpClient.request(resource, options)
  }

  // 确认账单
  async apiSupplierBillsConfirm(options = {}) {
    const resource = `/api/supplier/bills/confirm`
    return this.httpClient.request(resource, options)
  }

  // 删除账单
  async apiSupplierBillsDelete(options = {}) {
    const resource = `/api/supplier/bills/delete`
    return this.httpClient.request(resource, options)
  }

  // 下载其他费用导入模板
  async apiSupplierBillsImportTemplate(options = {}) {
    options.method = 'GET'
    const resource = `/api/supplier/bills/other-fees/import/template`
    return this.httpClient.request(resource, options)
  }

  // 导入其他费用数据
  async apiSupplierBillsImportPreview(options = {}) {
    const resource = `/api/supplier/bills/other-fees/import/preview`
    return this.httpClient.request(resource, options)
  }

  // 获取账单薪酬明细
  async apiSupplierBillSalaryDetails(options = {}) {
    const resource = `/api/supplier/bills/salary-details`
    return this.httpClient.request(resource, options)
  }

  // 获取账单管理费明细
  async apiSupplierBillsManagementFeeDetails(options = {}) {
    const resource = `/api/supplier/bills/management-fee-details`
    return this.httpClient.request(resource, options)
  }

  // 获取账单其他费用明细
  async apiSupplierBillsOtherFeeDetails(options = {}) {
    const resource = `/api/supplier/bills/other-fee-details`
    return this.httpClient.request(resource, options)
  }

  // 电子合同列表
  async supplierProtocolList(options = {}) {
    const resource = `/api/supplier/protocol/list`
    return this.httpClient.request(resource, options)
  }

  // 发起签署
  async supplierProtocolSignInit(options = {}) {
    const resource = `/api/supplier/protocol/signInit`
    return this.httpClient.request(resource, options)
  }

  // 签约二维码
  async supplierProtocolGenerateQrCode(options = {}) {
    const resource = `/api/supplier/protocol/generateQrCode`
    return this.httpClient.request(resource, options)
  }

  // 薪酬计算列表
  async supplierSalaryListPayroll(options = {}) {
    const resource = `/api/supplier/salary/listPayroll`
    return this.httpClient.request(resource, options)
  }

  // 薪酬计算详情
  async supplierSalaryListPayrollDetail(options = {}) {
    const resource = `/api/supplier/salary/listPayrollDetail`
    return this.httpClient.request(resource, options)
  }

  // 上期收入与减除
  async supplierSalaryListPreviousIncomeDeduction(options = {}) {
    const resource = `/api/supplier/salary/listPreviousIncomeDeduction`
    return this.httpClient.request(resource, options)
  }

  // 上期收入减除导入
  async supplierSalaryPreviousIncomeImport(options = {}) {
    const resource = `/api/supplier/salary/previousIncomeImport`
    return this.httpClient.request(resource, options)
  }

  // 创建工资表
  async supplierSalaryAddPayroll(options = {}) {
    const resource = `/api/supplier/salary/addPayroll`
    return this.httpClient.request(resource, {
      ...options,
      requestInterceptor(resource, options) {
        delete options.headers['Content-Type']
        return [null, resource, options]
      }
    })
  }

  // 下载模板
  async supplierSalaryDownloadTemplate(taxCalculationMethod, options = {}) {
    const resource = `/api/supplier/salary/download/template?taxCalculationMethod=${taxCalculationMethod}`
    return this.httpClient.request(resource, options)
  }

  // 下载错误文件
  async supplierSalaryImportVerifyErrorLog(uuid, options = {}) {
    const resource = `/api/supplier/salary/importVerifyErrorLog/${uuid}`
    return this.httpClient.request(resource, options)
  }

  // 下载错误文件
  async supplierBillsImportVerifyErrorLog(uuid, options = {}) {
    const resource = `/api/supplier/bills/importVerifyErrorLog/${uuid}`
    return this.httpClient.request(resource, options)
  }

  // 确认算税结果
  async supplierSalaryConfirmPayroll(options = {}) {
    const resource = `/api/supplier/salary/confirmPayroll`
    return this.httpClient.request(resource, options)
  }

  // 删除工资表
  async supplierSalaryDeletePayroll(options = {}) {
    const resource = `/api/supplier/salary/deletePayroll`
    return this.httpClient.request(resource, options)
  }

  // 查询人员
  async supplierSalarySalaryOverStaffCount(options = {}) {
    const resource = `/api/supplier/salary/salaryOverStaffCount`
    return this.httpClient.request(resource, options)
  }

  // 创建代发批次
  async supplierCreateBatch(options = {}) {
    const resource = `/api/supplier/proxy/createBatch`
    return this.httpClient.request(resource, options)
  }

  // 代发记录列表
  async supplierListProxyBatch(options = {}) {
    const resource = `/api/supplier/proxy/listProxyBatch`
    return this.httpClient.request(resource, options)
  }

  // 查询代发批次
  async supplierListProxy(options = {}) {
    const resource = `/api/supplier/proxy/listProxy`
    return this.httpClient.request(resource, options)
  }

  // 查询批次汇总信息
  async supplierBatchSummary(options = {}) {
    const resource = `/api/supplier/proxy/batchSummary`
    return this.httpClient.request(resource, options)
  }

  // 确认支付
  async supplierConfirmPay(options = {}) {
    const resource = `/api/supplier/proxy/confirmPay`
    return this.httpClient.request(resource, options)
  }

  // 删除批次
  async supplierDeleteBatch(options = {}) {
    const resource = `/api/supplier/proxy/deleteBatch`
    return this.httpClient.request(resource, options)
  }

  // 发票列表
  async supplierInvoicesList(options = {}) {
    const resource = `/api/supplier/invoices/list`
    return this.httpClient.request(resource, options)
  }

  // OCR身份证识别
  async ocrIdentify(options = {}) {
    const resource = `/api/public/ocrIdentify`
    return this.httpClient.request(resource, options)
  }

  // OCR识别并验证
  async supplierLaborOcrVerify(options = {}) {
    const resource = `/api/personal/labor/ocrVerify`
    return this.httpClient.request(resource, options)
  }

  // 活体人脸识别
  async apiFaceAuth(options = {}) {
    const resource = `/api/personal/protocol/faceAuth`
    return this.httpClient.request(resource, options)
  }

  // 人员待签署列表查询
  async supplierProtocolGetLaborContract(options = {}) {
    const resource = `/api/personal/protocol/getLaborContract`
    return this.httpClient.request(resource, options)
  }

  // h5个人签署
  async supplierProtocolSign(options = {}) {
    const resource = `/api/personal/protocol/sign`
    return this.httpClient.request(resource, options)
  }
  // 发票-确认开票
  async supplierInvoicesUploadFile(options = {}) {
    const resource = `/api/supplier/invoices/upload-file`
    return this.httpClient.request(resource, options)
  }

  // 发票-退回
  async supplierInvoicesReturn(options = {}) {
    const resource = `/api/supplier/invoices/return`
    return this.httpClient.request(resource, options)
  }

  // 发票-预设信息
  async supplierInvoicesPresetInfo(options = {}) {
    const resource = `/api/supplier/invoices/preset-info`
    return this.httpClient.request(resource, options)
  }
  // 发票-申请开票
  async supplierInvoicesApply(options = {}) {
    const resource = `/api/supplier/invoices/apply`
    return this.httpClient.request(resource, options)
  }

  // 发票-详情
  async supplierInvoicesDetail(options = {}) {
    const resource = `/api/supplier/invoices/detail`
    return this.httpClient.request(resource, options)
  }

  // 商户端-分页查询开票列表
  async costomerInvoicesList(options = {}) {
    const resource = `/api/costomer/invoices/list`
    return this.httpClient.request(resource, options)
  }

  // 商户端-获取开票申请详情
  async costomerInvoicesDetail(options = {}) {
    const resource = `/api/costomer/invoices/detail`
    return this.httpClient.request(resource, options)
  }

  // // 人员待签署列表查询
  // async supplierProtocolGetLaborContract(options = {}) {
  //   const resource = `/api/supplier/protocol/getLaborContract`
  //   return this.httpClient.request(resource, options)
  // }

  // 获取作业主体配置详情（包含支付通道）
  async corporationConfigDetail(options = {}) {
    const resource = `/api/supplier/corporationConfigDetail`
    return this.httpClient.request(resource, options)
  }

  // 新增客户白名单账户
  async addCustomerWhiteAccount(options = {}) {
    const resource = `/api/supplier/customer/addCustomerWhiteAccount`
    return this.httpClient.request(resource, options)
  }

  // 查询客户白名单账户列表
  async queryCustomerWhiteAccount(options = {}) {
    const resource = `/api/supplier/customer/customerWhiteAccount`
    return this.httpClient.request(resource, options)
  }

  // 删除客户白名单账户
  async deleteCustomerWhiteAccount(options = {}) {
    const resource = `/api/supplier/customer/deleteCustomerWhiteAccount`
    return this.httpClient.request(resource, options)
  }

  // 查询客户账户余额明细
  async accountBalanceList(options = {}) {
    const resource = `/api/supplier/customer/accountBalanceList`
    return this.httpClient.request(resource, options)
  }

  // 获取客户关联的作业主体列表
  async customerCorporation(options = {}) {
    const resource = `/api/supplier/customer/customerCorporation`
    return this.httpClient.request(resource, options)
  }
}

export default Client