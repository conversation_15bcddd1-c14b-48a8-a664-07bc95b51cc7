<template>
  <div class="salary-new-container" v-loading="pageLoading" element-loading-text="加载中...">
    <div class="form-container">
      <el-form :model="form" :rules="rules" ref="form" label-width="120px" class="salary-form">

        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-title">工资表信息</div>

          <el-form-item label="服务合同" prop="contractId">
            <el-select
              v-model="form.contractId"
              placeholder="请选择服务合同"
              style="width: 100%"
              filterable
            >
              <el-option
                v-for="contract in contractOptions"
                :key="contract.id"
                :label="contract.name"
                :value="contract.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="税款所属期" prop="taxPeriod">
            <el-date-picker
              v-model="form.taxPeriod"
              type="month"
              placeholder="选择月份"
              format="yyyy-MM"
              value-format="yyyy-MM"
              style="width: 100%"
              @change="onTaxPeriodChange"
            />
          </el-form-item>

          <el-form-item label="个税申报月">
            <el-input :value="taxDeclarationMonth" readonly />
          </el-form-item>

          <el-form-item label="上传应发文件" prop="file">
            <div style="display: flex;flex-direction: column;align-items: baseline;">
            <div class="upload-section" style="display: flex;justify-content: flex-start" >
              <div class="upload-main-area">
                <el-upload
                  ref="upload"
                  class="upload-dragger"
                  drag
                  :auto-upload="false"
                  :file-list="fileList"
                  :show-file-list="false"
                  accept=".xlsx,.xls"
                  :on-change="handleFileChange"
                  :on-remove="onRemove"
                  :before-upload="beforeUpload"
                  :limit="1"
                  :on-exceed="onExceed"
                >
                  <div class="upload-content">
                    <img src="../../../assets/images/upload-excel.svg" alt=""/>
                    <el-button
                      class="upload-select-btn"
                      plain
                    >选择文件</el-button>
                    <div class="upload-tip">
                      支持xlsx和xls文件
                    </div>
                  </div>
                </el-upload>

                <div class="upload-actions">
                  <el-button
                    type="text"
                    @click="downloadTemplate"
                    class="template-download-btn"
                  >
                    <i class="el-icon-download"></i>
                    下载模板
                  </el-button>
                </div>
              </div>
            </div>
            <!-- 自定义文件列表显示 -->
              <div v-if="fileList.length > 0" >
                <div
                  v-for="(file, index) in fileList"
                  :key="index"
                  class="file-item"
                >
                  <span class="file-name">
                    <i class="el-icon-document file-icon"></i>
                    {{ file.name }}
                  </span>
                  <i
                    class="el-icon-close remove-icon"
                    @click="removeFile(index)"
                    title="删除文件"
                  ></i>
                </div>
              </div>
              </div>
          </el-form-item>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button type="primary" @click="onSubmit" :loading="submitting">
            创建工资表
          </el-button>
          <el-button @click="onCancel">取消</el-button>
        </div>

      </el-form>
    </div>

    <!-- 导入结果对话框 -->
    <el-dialog
      title="应发文件导入结果"
      :visible.sync="showErrorDialog"
      width="800px"
      :close-on-click-modal="false"
      class="import-result-dialog"
    >
      <div class="import-result-container">
        <div v-if="importResult.success" class="result-content success-content">
          <div class="result-icon success-icon">
            <i class="el-icon-circle-check"></i>
          </div>
          <div class="result-text">
            <h2>导入成功</h2>
            <p>共导入 <span class="highlight-number">{{ importResult.successCount }}</span> 条数据</p>
          </div>
        </div>

        <div v-else class="result-content error-content">
          <div class="result-icon error-icon">
            <i class="el-icon-circle-close"></i>
          </div>
          <div class="result-text">
            <h2>导入结果：成功 {{ importResult.successCount  }} 条，失败 {{ importResult.failCount || 0 }} 条</h2>
            <p class="error-description">您可以导出错误数据，查看失败原因</p>
          </div>
          <div class="action-area">
            <el-button type="primary" @click="downloadErrorFile" class="download-btn">
              导出错误数据
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import makeClient from '../../../services/operateLabor/makeClient'
import { getToken } from '../../../helpers/token'

const client = makeClient()

// 获取上一个月的 YYYY-MM 格式字符串
function getPreviousMonth() {
  const date = new Date()
  date.setMonth(date.getMonth() - 1)
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  return `${year}-${month}`
}

export default {
  data() {
    return {
      pageLoading: true,
      submitting: false,
      form: {
        contractId: '',
        taxPeriod: getPreviousMonth(),
        file: null
      },
      rules: {
        contractId: [{ required: true, message: '请选择服务合同', trigger: 'change' }],
        taxPeriod: [{ required: true, message: '请选择税款所属期', trigger: 'change' }],
        file: [{ required: true, message: '请选择应发文件', trigger: 'change' }]
      },
      contractOptions: [],
      taxDeclarationMonth: '',
      fileList: [],
      headerToken: {
        Authorization: `Bearer ${getToken()}`
      },
      showErrorDialog: false,
      importResult: {
        success: false,
        successCount: 0,
        failCount: 0,
        payrollId: null,
        errorUuid: ''
      }
    }
  },
  async created() {
    try {
      await this.loadOptions()
      this.onTaxPeriodChange() // 初始化个税申报月
    } finally {
      this.pageLoading = false
    }
  },
  methods: {
    async loadOptions() {
      // 加载合同选项
      const [err, res] = await client.customerGetAllContracts({ body: {} })
      if (!err) {
        this.contractOptions = res.data || []
      }
    },

    onTaxPeriodChange() {
      if (this.form.taxPeriod) {
        // 计算个税申报月（税款所属期的下一个月）
        const [year, month] = this.form.taxPeriod.split('-')
        const nextMonth = parseInt(month) + 1
        if (nextMonth > 12) {
          this.taxDeclarationMonth = `${parseInt(year) + 1}-01`
        } else {
          this.taxDeclarationMonth = `${year}-${nextMonth.toString().padStart(2, '0')}`
        }
      } else {
        this.taxDeclarationMonth = ''
      }
    },

    handleFileChange(file, fileList) {
      this.fileList = fileList
      if (fileList.length > 0) {
        this.form.file = fileList[0].raw || fileList[0]
      } else {
        this.form.file = null
      }
      this.$refs.form.validateField('file')
    },

    beforeUpload(file) {
      // 检查文件大小（5MB = 5 * 1024 * 1024 bytes）
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isLt5M) {
        this.$message.error('上传文件大小不能超过 5MB!')
        return false
      }

      // 检查文件格式
      const isExcel = file.type === 'application/vnd.ms-excel' ||
                     file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      if (!isExcel) {
        this.$message.error('只能上传 Excel 文件!')
        return false
      }

      // 阻止自动上传
      return false
    },

    onExceed(files, fileList) {
      this.$message.warning('只能上传一个文件，请先删除已上传的文件再重新上传')
    },

    onRemove(file, fileList) {
      this.fileList = fileList
      this.form.file = null
      this.$refs.form.validateField('file')
    },

    removeFile(index) {
      // 从文件列表中移除指定索引的文件
      this.fileList.splice(index, 1)
      this.form.file = null
      // 同时清空上传组件的文件列表
      if (this.$refs.upload && typeof this.$refs.upload.clearFiles === 'function') {
        this.$refs.upload.clearFiles()
      }
      this.$refs.form.validateField('file')
    },

    async downloadTemplate() {
      try {
        // 使用fetch API携带token下载文件
        const token = this.headerToken.Authorization
        const response = await fetch(`${window.env?.apiPath}/api/customer/salary/download/template`, {
          method: 'POST',
          headers: {
            'Authorization': token,
            'Content-Type': 'application/octet-stream'
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        // 获取文件blob
        const blob = await response.blob()

        // 创建下载链接
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = '工资表模板.xlsx'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // 清理URL对象
        window.URL.revokeObjectURL(url)
      } catch (error) {
        console.error('下载模板失败：', error)
        this.$message.error('下载模板失败')
      }
    },

    async onSubmit() {
      const valid = await this.$refs.form.validate()
      if (!valid) {
        return
      }

      this.submitting = true

      try {
        const formData = new FormData()
        formData.append('contractId', this.form.contractId)
        formData.append('taxPeriod', this.form.taxPeriod)
        formData.append('file', this.form.file)

        const [err, res] = await client.customerSalaryAddPayroll({
          body: formData
        })

        if (err) {
          console.error('创建工资表失败：', err)
          this.$message.error(err.message || '创建失败，请重试')
          return
        }



        // 检查响应数据中的错误情况
        if (res && res.success && res.data) {
          const { successCount, failCount, uuid } = res.data

          if (failCount === 0) {
            // 全部成功
            this.$message.success(`成功创建工资表，共导入${successCount || '所有'}条数据`)
            this.$router.push('/salaryCalculate')
          } else if (failCount > 0 && uuid) {
            // 有失败的数据，显示错误对话框
            this.importResult = {
              success: false,
              successCount,
              failCount,
              errorUuid: uuid
            }
            this.showErrorDialog = true
          } else {
            // 有失败但没有uuid
            this.$message.error(`创建失败，共${failCount}条数据有问题`)
          }
        } else {
          this.$message.success('工资表创建成功')
          this.$router.push('/salaryCalculate')
        }
      } catch (error) {
        console.error('创建工资表失败：', error)
        this.$message.error('创建工资表失败，请重试')
      } finally {
        this.submitting = false
      }
    },

    async downloadErrorFile() {
      try {
        // 使用fetch API携带token下载错误文件
        const token = this.headerToken.Authorization
        const response = await fetch(`${window.env?.apiPath}/api/customer/salary/importVerifyErrorLog/${this.importResult.errorUuid}`, {
          method: 'POST',
          headers: {
            'Authorization': token,
            'Content-Type': 'application/octet-stream'
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        // 获取文件blob
        const blob = await response.blob()

        // 创建下载链接
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = '工资表导入错误数据.xlsx'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // 清理URL对象
        window.URL.revokeObjectURL(url)
      } catch (error) {
        console.error('下载错误文件失败：', error)
        this.$message.error('下载错误文件失败')
      }
    },

    goToDetail() {
      if (this.importResult.payrollId) {
        this.$router.push(`/salaryCalculate/${this.importResult.payrollId}`)
      } else {
        this.$router.push('/salaryCalculate')
      }
    },

    onCancel() {
      this.$router.back()
    }
  }
}
</script>

<style scoped>
.salary-new-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 100vh;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  box-sizing: border-box;
}

.form-container {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  width: 100%;
  max-width: 1000px;
  min-height: 600px;
}

.salary-form {
  width: 100%;
}

.form-section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 24px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
  display: inline-block;
}

.el-form-item {
  margin-bottom: 24px;
}

.el-form-item__label {
  font-size: 14px;
  color: #606266;
  font-weight: 550;
  line-height: 32px;
}

.el-input,
.el-select,
.el-date-picker {
  width: 100%;
}

/* 文件上传样式 */
.upload-section {
  width: 100%;
}

.upload-main-area {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.upload-dragger {
  flex: 1;
}

.upload-dragger .el-upload {
  width: 100%;
}

.upload-dragger .el-upload-dragger {
  width: 100%;
  height: 180px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background-color: #fafafa;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.upload-dragger .el-upload-dragger:hover {
  border-color: #409EFF;
  background-color: #f0f9ff;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  padding: 30px 0 24px;
}

.upload-select-btn {
  width: 88px;
  margin: 10px 0 8px;
  border: 1px solid #cad0dbff;
  color: #1e2228;
  display: flex;
  justify-content: center;
}

.upload-tip {
  color: #828b9b;
  font-size: 14px;
  text-align: center;
}

.upload-actions {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  height: 180px;
  padding-bottom: 8px;
}

.template-download-btn {
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 4px;
  transition: all 0.3s;
  white-space: nowrap;
}

.template-download-btn:hover {
  background-color: #ecf5ff;
  transform: translateY(-1px);
}

/* 自定义文件列表样式 */
.custom-file-list {
  margin-top: 10px;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2px 12px;
  border-radius: 4px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
}

.file-name {
  flex: 1;
  color: #606266;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: center;
}

.file-icon {
  margin-right: 5px;
  color: #909399;
}

.remove-icon {
  color: #f56c6c;
  font-size: 14px;
  cursor: pointer;
  margin-left: 8px;
  padding: 2px;
  transition: all 0.3s;
}

.remove-icon:hover {
  color: #f78989;
  background-color: #fef0f0;
  border-radius: 4px;
}

.form-actions {
  text-align: center;
  padding-top: 32px;
  border-top: 1px solid #ebeef5;
  margin-top: 40px;
}

.form-actions .el-button {
  margin: 0 12px;
  padding: 12px 32px;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.3s;
}

.form-actions .el-button--primary {
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
}

.form-actions .el-button--primary:hover {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  transform: translateY(-1px);
}

.form-actions .el-button:not(.el-button--primary) {
  color: #606266;
  border-color: #dcdfe6;
  background-color: #fff;
}

.form-actions .el-button:not(.el-button--primary):hover {
  color: #409EFF;
  border-color: #409EFF;
  background-color: #ecf5ff;
  transform: translateY(-1px);
}

/* 导入结果对话框样式 */
.import-result-dialog {
  border-radius: 12px;
}

.import-result-dialog .el-dialog {
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.import-result-dialog .el-dialog__header {
  padding: 24px 32px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.import-result-dialog .el-dialog__title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.import-result-dialog .el-dialog__body {
  padding: 0;
}

.import-result-container {
  padding: 40px 40px;
  text-align: center;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.result-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  width: 100%;
}

.result-icon {
  font-size: 80px;
  margin-bottom: 8px;
}

.success-icon {
  color: #67c23a;
}

.error-icon {
  color: #f56c6c;
}

.result-text h2 {
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.result-text p {
  margin: 0;
  font-size: 16px;
  color: #606266;
  line-height: 1.6;
}

.highlight-number {
  color: #409EFF;
  font-weight: 600;
  font-size: 18px;
}

.error-description {
  color: #909399;
  margin-top: 8px !important;
}

.action-area {
  margin-top: 32px;
}

.download-btn {
  padding: 12px 32px;
  font-size: 16px;
  border-radius: 6px;
  font-weight: 500;
}

.dialog-footer {
  padding: 20px 32px 32px;
  text-align: center;
  border-top: 1px solid #f0f0f0;
}

.dialog-footer .el-button {
  padding: 10px 24px;
  font-size: 14px;
  border-radius: 6px;
  margin: 0 8px;
}

.close-btn {
  color: #606266;
  border-color: #dcdfe6;
}

.detail-btn {
  font-weight: 500;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .salary-new-container {
    padding: 20px 12px;
  }

  .form-container {
    padding: 24px;
  }

  .el-form-item__label {
    width: 100px !important;
  }

  .upload-main-area {
    flex-direction: column;
    gap: 12px;
  }

  .upload-dragger .el-upload-dragger {
    height: 140px;
  }

  .upload-content {
    padding: 20px 0 16px;
  }

  .upload-select-btn {
    width: 80px;
    margin: 8px 0 6px;
    font-size: 13px;
  }

  .upload-tip {
    font-size: 12px;
  }

  .upload-actions {
    height: auto;
    padding-bottom: 0;
    justify-content: center;
    align-items: center;
  }

  .template-download-btn {
    font-size: 13px;
    padding: 6px 12px;
  }

  .file-item {
    padding: 6px 10px;
  }

  .file-name {
    font-size: 13px;
  }

  .remove-icon {
    font-size: 13px;
  }

  .import-result-dialog {
    width: 95% !important;
  }

  .import-result-container {
    padding: 40px 20px;
    min-height: 250px;
  }

  .result-icon {
    font-size: 60px;
  }

  .result-text h2 {
    font-size: 18px;
  }

  .result-text p {
    font-size: 14px;
  }

  .download-btn {
    padding: 10px 24px;
    font-size: 14px;
  }
}
</style>
