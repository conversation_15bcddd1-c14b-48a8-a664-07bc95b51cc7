<template>
  <div class="filter-tags">
    <div style="padding: 10px 18px; background: #fff">
      <h3
        style="
          font-size: 15px;
          font-weight: 600;
          color: #262935;
          margin: 20px 0;
        "
      >
        筛选标签
      </h3>
      <div
        style="
          display: flex;
          flex-wrap: wrap;
          align-content: flex-start;
          gap: 16px;
          height: calc(100vh - 160px);
          overflow: scroll;
        "
      >
        <div
          style="
            flex: 0 0 calc(33.3333% - 11px);
            height: 83px;
            box-sizing: border-box;
            background: #ecf6ff;
            text-align: center;
            border-radius: 4px;
          "
          v-for="(item, index) in tagOptions"
          :key="index"
        >
          <div
            :class="[item.tagId === activeTagId ? 'curr' : 'item-block']"
            @click="handleSelected(item.tagId)"
          >
            <img
              :src="item.iconUrl"
              style="
                width: 22px;
                height: auto;
                position: absolute;
                bottom: 42px;
                right: 50%;
                margin-right: -11px;
              "
              alt=""
            />
            <div class="tags-name">{{ item.description }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="btns">
      <Button style="color: #0082ff; margin-right: 20px" @click="handleReset"
        >重置</Button
      >
      <Button type="primary" @click="handleConfirm">确认</Button>
    </div>
  </div>
</template>
<script>
import { Grid, GridItem, Button } from 'vant'

export default {
  components: {
    Grid,
    GridItem,
    Button
  },
  data() {
    return {
      tagOptions: [
        {
          tagId: 43,
          description: '市场推广',
          iconUrl:
            'https://156-dev.lanmaoly.com/gd/hrsaas/webapi/api/archive/v3/download/aa3b854a28e64f48b83cf2f24eb1a3cd/市场推广.png'
        },
        {
          tagId: 44,
          description: '信息技术服务',
          iconUrl:
            'https://156-dev.lanmaoly.com/gd/hrsaas/webapi/api/archive/v3/download/e5040383d7314dce8ea4ac235ca37b4a/技术软件设计.png'
        },
        {
          tagId: 45,
          description: '跑腿',
          iconUrl:
            'https://156-dev.lanmaoly.com/gd/hrsaas/webapi/api/archive/v3/download/cd053d69226842d6a9ce7bad5cc1ff74/跑腿.png'
        },
        {
          tagId: 46,
          description: '设计服务',
          iconUrl:
            'https://156-dev.lanmaoly.com/gd/hrsaas/webapi/api/archive/v3/download/7eb1fa0926c14790875211e94d1b0036/任务标签通用符.png'
        },
        {
          tagId: 47,
          description: '信息服务',
          iconUrl:
            'https://156-dev.lanmaoly.com/gd/hrsaas/webapi/api/archive/v3/download/a9c501c707ce4a129b365574bafbd467/知识分享.png'
        },
        {
          tagId: 48,
          description: '装卸搬运',
          iconUrl:
            'https://156-dev.lanmaoly.com/gd/hrsaas/webapi/api/archive/v3/download/61e95bd15f2b47aab5f11836d25debbb/养护维修.png'
        },
        {
          tagId: 49,
          description: '网络营销',
          iconUrl:
            'https://156-dev.lanmaoly.com/gd/hrsaas/webapi/api/archive/v3/download/67e1aa48cf2c49cebf1dcb2b11a5a036/a.png'
        },
        {
          tagId: 50,
          description: '美容化妆',
          iconUrl:
            'https://156-dev.lanmaoly.com/gd/hrsaas/webapi/api/archive/v3/download/7ae734f600714df2afa50449e7e29954/美容化妆.png'
        },
        {
          tagId: 51,
          description: '生产加工',
          iconUrl:
            'https://156-dev.lanmaoly.com/gd/hrsaas/webapi/api/archive/v3/download/f0754d4570e045499faa6d83e691360c/生产加工.png'
        },
        {
          tagId: 52,
          description: '钟点工',
          iconUrl:
            'https://156-dev.lanmaoly.com/gd/hrsaas/webapi/api/archive/v3/download/03841be918a749f6b38d1f08fe86e55b/钟点工.png'
        },
        {
          tagId: 53,
          description: '水电安装',
          iconUrl:
            'https://156-dev.lanmaoly.com/gd/hrsaas/webapi/api/archive/v3/download/aef8a2ca63434294bf1ab4ad6025cd03/包装设计.png'
        }
      ],
      activeTagId: ''
    }
  },
  methods: {
    handleSelected(tagId) {
      this.activeTagId = tagId
    }
  }
}
</script>
<style scoped>
.tags-name {
  width: 80px;
  height: 35px;
  text-align: center;
  font-size: 13px;
  font-weight: 400;
  color: rgba(38, 41, 53, 1);
  position: absolute;
  bottom: 0px;
  right: 50%;
  margin-right: -40px;
}
.curr {
  height: 83px;
  color: rgba(38, 41, 53, 1);
  border-radius: 4px;
  border: 1px solid #0082ff;
  background: #ecf6ff;
  font-size: 13px;
  font-weight: 400;
  position: relative;
}
.item-block {
  height: 83px;
  color: rgba(38, 41, 53, 1);
  font-size: 13px;
  font-weight: 400;
  position: relative;
}
.btns {
  position: fixed;
  width: 100%;
  box-sizing: border-box;
  bottom: 0;
  padding: 20px;
  background: #ffffff;
  box-shadow: 0 -2px 4px 0 #edf1f8;
}
.btns button {
  border: 1px solid #0082ff !important;
  border-radius: 20px;
  width: 150px;
  height: 40px;
}
</style>
