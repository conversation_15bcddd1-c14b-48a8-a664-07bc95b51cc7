<template>
  <div class="attachment-display-container">
    <!-- 文件预览网格 -->
    <div class="file-grid">
      <!-- 已有的文件ID（返显） -->
      <div
        v-for="(fileId, index) in existingFileIds"
        :key="`existing-${fileId}`"
        class="file-preview-item"
      >
        <!-- 图片预览 -->
        <div v-if="!fileLoadErrors[fileId]" class="image-preview">
          <div class="preview-image-container">
            <img
              :src="getFilePreviewUrl(fileId)"
              :alt="`附件${index + 1}`"
              class="preview-image"
              @error="handleImageError(fileId)"
              @load="handleImageLoad(fileId)"
            />
          </div>
          <div class="file-info">
            <span class="file-name" :title="`附件${index + 1}`">附件{{ index + 1 }}</span>
          </div>
          <div class="file-overlay">
            <div class="file-actions">
              <i class="el-icon-zoom-in" @click="previewImage(fileId)" title="预览"></i>
              <i class="el-icon-download" @click="downloadFile(fileId)" title="下载"></i>
              <i v-if="!readonly" class="el-icon-delete" @click="removeExistingFile(fileId)" title="删除"></i>
            </div>
          </div>
        </div>

        <!-- 非图片文件预览 -->
        <div v-else class="file-preview">
          <div class="file-icon-container">
            <i
              :class="getFileIcon().icon"
              class="file-type-icon"
              :style="{ color: getFileIcon().color }"
            ></i>
          </div>
          <div class="file-info">
            <span class="file-name" :title="`附件${index + 1}`">附件{{ index + 1 }}</span>
          </div>
          <div class="file-overlay">
            <div class="file-actions">
              <i class="el-icon-download" @click="downloadFile(fileId)" title="下载"></i>
              <i v-if="!readonly" class="el-icon-delete" @click="removeExistingFile(fileId)" title="删除"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 新上传的文件 -->
      <div
        v-for="(file, index) in newFileList"
        :key="`new-${index}`"
        class="file-preview-item"
      >
        <!-- 图片预览 -->
        <div v-if="isImageFile(file)" class="image-preview">
          <div class="preview-image-container">
            <img :src="getFilePreviewUrl(file)" :alt="file.name" class="preview-image" />
          </div>
          <div class="file-info">
            <span class="file-name" :title="file.name">{{ file.name }}</span>
          </div>
          <div class="file-overlay">
            <div class="file-actions">
              <i class="el-icon-zoom-in" @click="previewNewImage(file)" title="预览"></i>
              <i v-if="!readonly" class="el-icon-delete" @click="removeNewFile(index)" title="删除"></i>
            </div>
          </div>
        </div>

        <!-- 非图片文件预览 -->
        <div v-else class="file-preview">
          <div class="file-icon-container">
            <i
              :class="getFileIcon(file).icon"
              class="file-type-icon"
              :style="{ color: getFileIcon(file).color }"
            ></i>
          </div>
          <div class="file-info">
            <span class="file-name" :title="file.name">{{ file.name }}</span>
          </div>
          <div class="file-overlay">
            <div class="file-actions">
              <i class="el-icon-download" @click="downloadNewFile(file)" title="下载"></i>
              <i v-if="!readonly" class="el-icon-delete" @click="removeNewFile(index)" title="删除"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 上传按钮 -->
      <div v-if="totalFileCount < 10 && !readonly" class="upload-item">
        <el-upload
          ref="upload"
          class="file-upload"
          :headers="headerToken"
          :file-list="newFileList"
          :auto-upload="false"
          :on-change="onFileChange"
          :on-remove="onFileRemove"
          :before-upload="beforeUpload"
          accept=".pdf,.png,.jpg,.jpeg"
          :action="uploadUrl"
          :show-file-list="false"
          multiple
          drag
        >
          <div class="upload-box">
            <i class="el-icon-plus upload-icon"></i>
            <span class="upload-text">点击/拖拽文件至此区域</span>
          </div>
        </el-upload>
      </div>

      <!-- 无附件提示 -->
      <div v-if="totalFileCount === 0 && readonly" class="no-attachments">
        <i class="el-icon-document"></i>
        <span>暂无附件信息</span>
      </div>
    </div>
    
    <!-- 图片预览弹窗 -->
    <el-dialog
      :visible.sync="previewVisible"
      :show-close="true"
      :modal="true"
      :close-on-click-modal="true"
      width="800px"
      custom-class="image-preview-dialog"
      :title="previewImageName"
    >
      <img
        v-if="previewImageUrl"
        :src="previewImageUrl"
        style="max-width: 100%; max-height: 70vh"
      />
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '../../../../helpers/token'

export default {
  name: 'AttachmentDisplayComponent',
  props: {
    initialFileIds: {
      type: Array,
      default: () => []
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      existingFileIds: [], // 已有的文件ID列表
      newFileList: [], // 新上传的文件列表
      fileLoadErrors: {}, // 记录文件加载错误的对象
      headerToken: {
        Authorization: `Bearer ${getToken()}`
      },
      previewVisible: false,
      previewImageUrl: '',
      previewImageName: ''
    }
  },
  computed: {
    uploadUrl() {
      return `${window.env?.apiPath}/api/public/uploadFile`
    },
    totalFileCount() {
      return this.existingFileIds.length + this.newFileList.length
    }
  },
  watch: {
    initialFileIds: {
      handler(newFileIds) {
        if (newFileIds && newFileIds.length > 0) {
          this.existingFileIds = [...newFileIds]
        } else {
          this.existingFileIds = []
        }
      },
      immediate: true
    }
  },
  methods: {
    // 获取文件ID数组，用于提交时调用
    async getFileIds() {
      const fileIds = [...this.existingFileIds] // 保留已有的文件ID
      
      // 上传所有新文件
      for (const file of this.newFileList) {
        if (file.raw) {
          try {
            const response = await this.uploadSingleFile(file)
            if (response.success && response.data) {
              fileIds.push(response.data.fileId.toString())
            }
          } catch (error) {
            console.error('文件上传失败：', error)
            throw new Error(`文件 ${file.name} 上传失败`)
          }
        }
      }
      
      return fileIds
    },

    async uploadSingleFile(file) {
      return new Promise((resolve, reject) => {
        const formData = new FormData()
        formData.append('file', file.raw)

        const xhr = new XMLHttpRequest()
        xhr.open('POST', this.uploadUrl)
        xhr.setRequestHeader('Authorization', this.headerToken.Authorization)

        xhr.onload = () => {
          if (xhr.status === 200) {
            try {
              const response = JSON.parse(xhr.responseText)
              resolve(response)
            } catch (e) {
              reject(new Error('解析响应失败'))
            }
          } else {
            reject(new Error(`上传失败: ${xhr.status}`))
          }
        }

        xhr.onerror = () => {
          reject(new Error('网络错误'))
        }

        xhr.send(formData)
      })
    },

    handleImageError(fileId) {
      // 图片加载失败，标记为非图片文件
      this.$set(this.fileLoadErrors, fileId, true)
    },

    handleImageLoad(fileId) {
      // 图片加载成功，确保不在错误列表中
      this.$set(this.fileLoadErrors, fileId, false)
    },

    beforeUpload(file) {
      const isValidType = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'].includes(file.type)
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isValidType) {
        this.$message.error('只能上传 JPG、PNG、JPEG、PDF 格式的文件!')
        return false
      }
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB!')
        return false
      }
      return true
    },

    onFileChange(file, fileList) {
      // 限制文件数量
      const totalCount = this.existingFileIds.length + fileList.length
      if (totalCount > 10) {
        this.$message.warning('最多只能上传10个文件')
        return false // 阻止添加超出的文件
      }

      // 只处理新添加的文件，避免重复
      if (file.status === 'ready') {
        // 检查是否已经存在
        const exists = this.newFileList.some(existingFile =>
          existingFile.name === file.name && existingFile.size === file.size
        )

        if (!exists) {
          this.newFileList.push(file)
        }
      }

      this.emitFilesChange()
    },

    onFileRemove(file) {
      // 从新文件列表中移除指定文件
      this.newFileList = this.newFileList.filter(f =>
        !(f.name === file.name && f.size === file.size)
      )
      this.emitFilesChange()
    },

    removeNewFile(index) {
      this.newFileList.splice(index, 1)
      this.emitFilesChange()
    },

    removeExistingFile(fileId) {
      const index = this.existingFileIds.indexOf(fileId)
      if (index > -1) {
        this.existingFileIds.splice(index, 1)
        this.emitFilesChange()
      }
    },

    emitFilesChange() {
      // 合并已有文件ID和新文件，用于父组件监听
      const allFiles = [
        ...this.existingFileIds,
        ...this.newFileList.map(file => file.name)
      ]
      this.$emit('files-change', allFiles)
    },

    // 文件处理相关方法
    isImageFile(file) {
      const imageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
      return imageTypes.includes(file.raw?.type) || /\.(jpg|jpeg|png|gif|webp)$/i.test(file.name)
    },

    getFileIcon(file) {
      if (file) {
        const fileName = file.name.toLowerCase()
        // PDF文件
        if (fileName.endsWith('.pdf')) {
          return { icon: 'el-icon-document-copy', color: '#5470c6' }
        }
        // 图片文件
        else if (this.isImageFile(file)) {
          return { icon: 'el-icon-picture-outline', color: '#9c27b0' }
        }
      }
      // 默认文件图标（用于已有文件ID）
      return { icon: 'el-icon-document', color: '#409eff' }
    },

    getFilePreviewUrl(fileOrId) {
      if (typeof fileOrId === 'string' || typeof fileOrId === 'number') {
        // 已有文件ID
        return `${window.env?.apiPath}/api/public/previewFile/${fileOrId}`
      } else if (fileOrId.raw) {
        // 新上传的文件
        return URL.createObjectURL(fileOrId.raw)
      }
      return ''
    },

    previewImage(fileId) {
      const imageUrl = `${window.env?.apiPath}/api/public/previewFile/${fileId}`
      this.previewImageUrl = imageUrl
      this.previewImageName = '附件预览'
      this.previewVisible = true
    },

    previewNewImage(file) {
      this.previewImageUrl = this.getFilePreviewUrl(file)
      this.previewImageName = file.name
      this.previewVisible = true
    },

    downloadFile(fileId) {
      window.location.href = `${window.env?.apiPath}/api/public/downloadFile/${fileId}`
    },

    downloadNewFile(file) {
      // 对于新上传的文件，创建下载链接
      if (file.raw) {
        const url = URL.createObjectURL(file.raw)
        const link = document.createElement('a')
        link.href = url
        link.download = file.name
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)
      }
    },

    // 重置文件列表
    resetFiles() {
      this.existingFileIds = []
      this.newFileList = []
      this.fileLoadErrors = {}
      this.$nextTick(() => {
        if (this.$refs.upload) {
          this.$refs.upload.clearFiles()
        }
      })
    }
  }
}
</script>

<style scoped>
/* 复用 FileUploadComponent 的样式，并添加一些特定样式 */
.attachment-display-container {
  width: 100%;
  box-sizing: border-box;
}

.file-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-start;
}

.file-preview-item {
  position: relative;
  width: 160px;
  height: 140px;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #409eff;

    .file-overlay {
      opacity: 1;
    }
  }
}

.image-preview {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .preview-image-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    padding: 8px;

    .preview-image {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      border-radius: 4px;
    }
  }

  .file-info {
    padding: 8px 12px;
    background: #fff;
    border-top: 1px solid #f0f0f0;

    .file-name {
      font-size: 12px;
      color: #606266;
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.file-preview {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .file-icon-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    padding: 20px;

    .file-type-icon {
      font-size: 48px;
    }
  }

  .file-info {
    padding: 8px 12px;
    background: #fff;
    border-top: 1px solid #f0f0f0;

    .file-name {
      font-size: 12px;
      color: #606266;
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.file-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 8px;

  .file-actions {
    display: flex;
    gap: 16px;

    i {
      cursor: pointer;
      font-size: 20px;
      padding: 8px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
      }
    }
  }
}

.upload-item {
  width: 160px !important;
  height: 140px !important;
  flex-shrink: 0;
}

.file-upload {
  width: 100% !important;
  height: 100% !important;
  position: relative;
}

.upload-box {
  width: 160px !important;
  height: 140px !important;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafbfc;
  box-sizing: border-box;

  &:hover {
    border-color: #409eff;
    background: #f0f9ff;

    .upload-icon {
      color: #409eff;
    }

    .upload-text {
      color: #409eff;
    }
  }

  .upload-icon {
    font-size: 28px;
    color: #c0c4cc;
    margin-bottom: 8px;
    transition: color 0.3s ease;
  }

  .upload-text {
    font-size: 12px;
    color: #909399;
    text-align: center;
    line-height: 1.4;
    transition: color 0.3s ease;
    padding: 0 8px;
  }
}

.no-attachments {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #909399;
  width: 100%;

  i {
    font-size: 48px;
    margin-bottom: 16px;
  }

  span {
    font-size: 14px;
  }
}

/* 覆盖element-ui的默认拖拽样式 */
::v-deep .el-upload-dragger {
  width: 160px !important;
  height: 140px !important;
  padding: 0 !important;
  border: none !important;
}

/* 图片预览对话框样式 */
::v-deep .image-preview-dialog {
  text-align: center;
}

::v-deep .image-preview-dialog .el-dialog__body {
  padding: 20px;
}

::v-deep .image-preview-dialog .el-dialog__header {
  padding: 15px 20px;
}

::v-deep .image-preview-dialog img {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
}
</style>
