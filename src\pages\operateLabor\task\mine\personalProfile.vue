<template>
  <div style="height: 100vh; padding: 20px 10px; color: #333333">
    <div
      style="display: flex; justify-content: space-between; align-items: center"
    >
      <div>
        服务合同：<span style="color: #a5a5a5">{{ currentContractName }}</span>
      </div>
      <span style="color: #397de9" @click="showPicker = true">切换</span>
    </div>
    <div
      style="
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20px;
      "
      v-for="(item, index) in profileList"
      :key="index"
      @click="handleClick(item)"
    >
      {{ item }}
      <Icon name="arrow" />
    </div>

    <div class="btn">提交个人信息</div>

    <Popup v-model="showPicker" position="bottom">
      <Picker
        class="picker"
        title="切换服务合同"
        show-toolbar
        :default-index="defaultIndex"
        :columns="laborContractList"
        @cancel="showPicker = false"
        @confirm="onConfirm"
      />
    </Popup>
  </div>
</template>
<script>
import { Icon, Popup, Picker } from 'vant'
import handleError from 'kit/helpers/handleErrorH5'
import makeClient from 'kit/services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    Icon,
    Popup,
    Picker
  },
  data() {
    return {
      laborContractList: [],
      currentContractName: '',
      currentLaborInfoId: '',
      showPicker: false,
      defaultIndex: 0,
      profileList: ['基本信息：', '项目经历：', '附件信息：', '银行卡信息：']
    }
  },
  created() {
    this.init()
  },
  methods: {
    async init() {
      const [err, r] = await client.apiLaborLaborContractList()
      if (err) return handleError(err)
      this.laborContractList = r.data.map(item => ({
        text: item.contractName,
        value: item.contractId,
        laborInfoId: item.laborInfoId
      }))
      this.currentContractName = this.laborContractList[0].text
      this.currentLaborInfoId = this.laborContractList[0].laborInfoId
      this.defaultIndex = this.laborContractList.findIndex(item => item.laborInfoId === this.currentLaborInfoId)
    },
    onConfirm(value, index) {
      this.currentContractName = value.text
      this.currentLaborInfoId = value.laborInfoId
      this.defaultIndex = index
    },
    handleClick(item) {
      if (item === '基本信息：') {
        this.$router.push({
          path: '/personalProfile/basicInfo',
          query: {
            laborInfoId: this.currentLaborInfoId
          }
        })
      } else if (item === '项目经历：') {
        this.$router.push({
          path: '/personalProfile/projectExperience',
          query: {
            laborInfoId: this.currentLaborInfoId
          }
        })
      } else if (item === '附件信息：') {
        this.$router.push({
          path: '/personalProfile/attachmentInfo'
        })
      } else {
        this.$router.push({
          path: '/personalProfile/bankInfo'
        })
      }
    }
  }
}
</script>
<style scoped>
.btn {
  width: 300px;
  margin-bottom: 20px;
  margin-left: calc(50vw - 160px);
  padding: 10px 0;
  background: #2b7dfd;
  border-radius: 20px;
  text-align: center;
  color: #fff;
  position: fixed;
  bottom: 0;
}
</style>
