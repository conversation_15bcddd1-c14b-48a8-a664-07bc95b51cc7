<template>
  <div class="task-personnel-detail-container" v-loading="loading">
    <div class="detail-container">
      <!-- 任务基本信息 -->
      <div class="summary-container">
        <div class="summary-content">
          <div class="summary-left">
            <div class="summary-item">
              <label>任务名称：</label>
              <span>{{ taskInfo.taskName || '-' }}</span>
              <el-tag
                v-if="taskInfo.taskStatus"
                :type="getTaskStatusTagType(taskInfo.taskStatus)"
                size="small"
                effect="light"
                style="margin-left: 8px;"
              >
                {{ getTaskStatusText(taskInfo.taskStatus) }}
              </el-tag>
            </div>
            <div class="summary-item">
              <label>任务可见性：</label>
              <span>{{ taskInfo.taskVisibility || '-' }}</span>
            </div>
            <div class="summary-item">
              <label>所属服务合同：</label>
              <span>{{ taskInfo.businessContractName || '-' }}</span>
            </div>
            <div class="summary-item">
              <label>创建时间：</label>
              <span>{{ formatDateTime(taskInfo.createTime) }}</span>
            </div>
          </div>

          <!-- 右上角按钮 -->
          <div class="summary-actions">
            <!-- <el-button type="primary" size="small" @click="handleTaskQRCode">
              任务二维码
            </el-button> -->
            <el-button
              v-if="taskInfo.taskStatus === '进行中' || taskInfo.taskStatus === 'IN_PROGRESS'"
              type="primary"
              size="small"
              @click="handleCompleteTask"
            >
              完成任务
            </el-button>
            <el-button
              v-if="taskInfo.taskStatus === '已完成' || taskInfo.taskStatus === 'COMPLETED'"
              type="primary"
              size="small"
              @click="handleViewTaskCredentials"
              :loading="credentialsLoading"
            >
              任务完成凭证
            </el-button>
          </div>
        </div>
      </div>

      <!-- 人员列表 -->
      <div class="table-container">
        <!-- 搜索条件 -->
        <div class="search-container">
          <el-form :model="conditions" inline class="search-form">
            <el-form-item>
              <el-input
                v-model="conditions.filters.name"
                placeholder="请输入姓名"
                clearable
                @keyup.enter.native="onSearch"
              />
            </el-form-item>
            <el-form-item>
              <el-input
                v-model="conditions.filters.cellPhone"
                placeholder="请输入手机号"
                clearable
                @keyup.enter.native="onSearch"
              />
            </el-form-item>
            <el-form-item>
              <el-select
                v-model="conditions.filters.laborTaskStatus"
                placeholder="请选择任务状态"
                clearable
              >
                <el-option label="待审核" value="REVIEWING" />
                <el-option label="待接受" value="PENDING_ACCEPTANCE" />
                <el-option label="进行中" value="IN_PROGRESS" />
                <el-option label="已完成" value="COMPLETED" />
                <el-option label="已拒绝" value="REJECTED" />
              </el-select>
            </el-form-item>
            <el-form-item style="margin-left: 80px;">
              <el-button type="primary" @click="onSearch">查询</el-button>
              <el-button @click="onReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 表头操作区 -->
        <div class="table-header">
          <div class="table-actions">
            <el-button type="primary" @click="handleAssignPersonnel">
              指派人员
            </el-button>
            <el-button @click="handleBatchExport" :loading="exportLoading">
              批量导出人员
            </el-button>
          </div>
        </div>

        <el-table
          :data="tableData"
          :height="tableHeight"
          :stripe="false"
          highlight-current-row
          :header-cell-style="{background:'#f5f7fa', color: '#303133', fontWeight: '550'}"
          v-loading="tableLoading"
        >
          <template slot="empty">
            <div class="empty-data">暂无数据</div>
          </template>
          <el-table-column prop="name" label="姓名" min-width="120" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row.name || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="phoneNumber" label="手机号" min-width="140">
            <template slot-scope="scope">
              {{ scope.row.phoneNumber || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="claimType" label="认领类型" min-width="120">
            <template slot-scope="scope">
              {{ scope.row.claimType || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="laborTaskStatus" label="人员任务状态" min-width="120">
            <template slot-scope="scope">
              <el-tag
                :type="getLaborTaskStatusTagType(scope.row.laborTaskStatus)"
                size="medium"
                effect="light"
              >
                {{ getLaborTaskStatusText(scope.row.laborTaskStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="deliveryStatus" label="交付状态" min-width="120">
            <template slot-scope="scope">
              {{ scope.row.deliveryStatus || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="taskStartTime" label="任务开始时间" min-width="150">
            <template slot-scope="scope">
              {{ formatDateTime(scope.row.taskStartTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="sex" label="性别" min-width="80">
            <template slot-scope="scope">
              {{ scope.row.sex || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="age" label="年龄" min-width="80">
            <template slot-scope="scope">
              {{ scope.row.age || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="idCardNumber" label="身份证号" min-width="180" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row.idCardNumber || '-' }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="320" fixed="right">
            <template slot-scope="scope">
              <div class="action-buttons">
                <el-button
                  v-if="scope.row.laborTaskStatus === '待审核'"
                  type="text"
                  size="small"
                  @click="handleAuditPersonnel(scope.row)"
                >
                  审核人员
                </el-button>
                <el-button
                  type="text"
                  size="small"
                  @click="handleViewDelivery(scope.row)"
                >
                  查看交付
                </el-button>
                <el-button
                  type="text"
                  size="small"
                  @click="handleCompletePersonTask(scope.row)"
                >
                  完成任务
                </el-button>
                <el-button
                  type="text"
                  size="small"
                  @click="handleAuditDelivery(scope.row)"
                >
                  审核交付
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            :current-page="Math.floor(conditions.offset / conditions.limit) + 1"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="conditions.limit"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          />
        </div>
      </div>
    </div>

    <!-- 指派人员对话框 -->
    <el-dialog
      title="指派人员"
      :visible.sync="assignDialogVisible"
      width="800px"
      :close-on-click-modal="false"
      class="assign-personnel-dialog"
    >
      <div class="assign-content">
        <div class="assign-header">
          <span class="assign-title">按照指派人员信息模板填写人员信息后上传</span>
          <el-button style="margin-left: 10px;" @click="downloadTemplate" :loading="downloadLoading">
            下载模板
          </el-button>
        </div>

        <div class="upload-area">
          <el-upload
            ref="upload"
            class="upload-demo"
            drag
            action=""
            :auto-upload="false"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :file-list="fileList"
            :limit="1"
            :on-exceed="handleExceed"
            accept=".xls,.xlsx"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text"><span style="color: #4f71ff;">点击</span>或者拖拽文件到这里上传</div>
            <div class="el-upload__tip" slot="tip">
              支持格式：.xls、.xlsx格式文件，大小不能超过10M
            </div>
          </el-upload>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelAssign">取消</el-button>
        <el-button type="primary" @click="confirmAssign" :loading="uploadLoading">确定</el-button>
      </div>
    </el-dialog>

    <!-- 审核人员对话框 -->
    <el-dialog
      title="审核人员"
      :visible.sync="auditPersonnelDialogVisible"
      width="600px"
      :close-on-click-modal="false"
      class="audit-personnel-dialog"
    >
      <div class="audit-content">
        <el-form :model="auditForm" label-width="100px" style="width: 90%;">
          <el-form-item label="审核结果：" required>
            <el-select v-model="auditForm.auditResult" placeholder="请选择审核结果">
              <el-option label="审核通过" :value="true" />
              <el-option label="审核拒绝" :value="false" />
            </el-select>
          </el-form-item>

          <el-form-item
            v-if="auditForm.auditResult === false"
            label="拒绝原因："
            required
          >
            <el-input
              v-model="auditForm.auditResultDesc"
              type="textarea"
              :rows="4"
              placeholder="请输入拒绝原因"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelAuditPersonnel">取消</el-button>
        <el-button type="primary" @click="confirmAuditPersonnel" :loading="auditPersonnelLoading">确定</el-button>
      </div>
    </el-dialog>

    <!-- 完成任务组件 -->
    <CompleteTaskDialog
      :visible.sync="completeTaskDialogVisible"
      :task="currentCompleteTask"
      @complete="onTaskCompleted"
    />

    <!-- 任务完成凭证组件 -->
    <TaskCredentialsDialog
      :visible.sync="credentialsDialogVisible"
      :credentials-data="credentialsData"
      :loading="credentialsLoading"
    />
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import handleSuccess from '../../../helpers/handleSuccess'
import makeClient from '../../../services/operateLabor/makeClient'
import { getToken } from '../../../helpers/token'
import CompleteTaskDialog from './components/CompleteTaskDialog.vue'
import TaskCredentialsDialog from './components/TaskCredentialsDialog.vue'

const client = makeClient()

export default {
  components: {
    CompleteTaskDialog,
    TaskCredentialsDialog
  },
  data() {
    return {
      loading: true,
      tableLoading: false,
      taskInfo: {},
      tableData: [],
      total: 0,
      tableHeight: 500,
      // 指派人员对话框相关
      assignDialogVisible: false,
      downloadLoading: false,
      uploadLoading: false,
      fileList: [],
      // 批量导出相关
      exportLoading: false,
      // 审核人员对话框相关
      auditPersonnelDialogVisible: false,
      auditPersonnelLoading: false,
      currentAuditRow: null,
      auditForm: {
        auditResult: true, // 默认选择审核通过
        auditResultDesc: ''
      },
      // 完成任务相关
      finishTaskLoading: false,
      completeTaskDialogVisible: false,
      currentCompleteTask: null,
      // 任务完成凭证相关
      credentialsLoading: false,
      credentialsDialogVisible: false,
      credentialsData: {
        taskName: '',
        taskFinishTime: '',
        taskCredentials: []
      },
      headerToken: {
        Authorization: `Bearer ${getToken()}`
      },
      conditions: {
        offset: 0,
        limit: 10,
        sorts: [],
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          taskId: 0,
          name: '',
          cellPhone: '',
          laborTaskStatus: ''
        }
      }
    }
  },
  async created() {
    this.loading = true
    const taskId = this.$route.params.id
    this.conditions.filters.taskId = parseInt(taskId)
    
    await Promise.all([
      this.loadTaskInfo(),
      this.loadPersonnelList()
    ])
    this.loading = false
    this.setTableHeight()
    window.addEventListener('resize', this.setTableHeight)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.setTableHeight)
  },
  methods: {
    async loadTaskInfo() {
      const taskId = this.$route.params.id
      
      const [err, r] = await client.customerQueryTask({
        body: {
          offset: 0,
          limit: 10,
          sorts: [],
          withTotal: true,
          withDisabled: true,
          withDeleted: true,
          filters: {
            taskName: '',
            taskId: parseInt(taskId),
            taskStatus: '',
            businessContractId: '',
            taskVisibility: '',
            taskTagId: '',
            createTimeStart: null,
            createTimeEnd: null
          }
        }
      })

      if (err) {
        handleError(err)
        return
      }

      if (r.data && r.data.list && r.data.list.length > 0) {
        this.taskInfo = r.data.list[0] || {}
      }
    },

    async loadPersonnelList() {
      this.tableLoading = true

      const [err, r] = await client.customerQueryTaskLabor({
        body: this.conditions
      })

      this.tableLoading = false

      if (err) {
        handleError(err)
        return
      }

      this.tableData = r.data.list || []
      this.total = r.data.total || 0

      this.$nextTick(() => {
        this.setTableHeight()
      })
    },

    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.loadPersonnelList()
    },

    handleSizeChange(size) {
      this.conditions.limit = size
      this.conditions.offset = 0
      this.loadPersonnelList()
    },

    onSearch() {
      this.conditions.offset = 0
      this.loadPersonnelList()
    },

    onReset() {
      this.conditions.filters.name = ''
      this.conditions.filters.cellPhone = ''
      this.conditions.filters.laborTaskStatus = ''
      this.conditions.offset = 0
      this.loadPersonnelList()
    },

    formatDateTime(value) {
      if (!value) return '-'
      return new Date(value).toLocaleString('zh-CN')
    },

    // 获取劳务任务状态文本
    getLaborTaskStatusText(status) {
      const statusMap = {
        'REVIEWING': '待审核',
        'PENDING_ACCEPTANCE': '待接受',
        'IN_PROGRESS': '进行中',
        'COMPLETED': '已完成',
        'REJECTED': '已拒绝',
        // 兼容后端返回的中文状态
        '待审核': '待审核',
        '待接受': '待接受',
        '进行中': '进行中',
        '已完成': '已完成',
        '已拒绝': '已拒绝'
      }
      return statusMap[status] || status || '未知'
    },

    getTaskStatusText(status) {
      const statusMap = {
        'REVIEWING': '待审核',
        'PENDING_ACCEPTANCE': '待接受',
        'IN_PROGRESS': '进行中',
        'COMPLETED': '已完成',
        'REJECTED': '已拒绝',
        // 兼容旧的状态值
        'PENDING': '待接受',
        // 兼容任务状态
        '审核中': '审核中',
        '进行中': '进行中',
        '审核失败': '审核失败',
        '已完成': '已完成',
        '已停用': '已停用'
      }
      return statusMap[status] || status || '-'
    },

    getTaskStatusTagType(status) {
      const typeMap = {
        'REVIEWING': 'warning',
        'PENDING_ACCEPTANCE': 'warning',
        'IN_PROGRESS': 'primary',
        'COMPLETED': 'success',
        'REJECTED': 'danger',
        // 兼容旧的状态值
        'PENDING': 'warning',
        // 兼容任务状态
        '审核中': 'warning',
        '进行中': 'success',
        '审核失败': 'danger',
        '已完成': 'success',
        '已停用': 'info'
      }
      return typeMap[status] || 'info'
    },

    getLaborTaskStatusTagType(status) {
      const typeMap = {
        '待接受': 'warning',
        '审核中': 'warning',
        '进行中': 'success',
        '审核失败': 'danger',
        '已完成': 'success',
        '已停用': 'info'
      }
      return typeMap[status] || 'info'
    },

    // 操作方法 - 先写占位符
    handleTaskQRCode() {
      this.$message.info('任务二维码功能待实现')
    },

    handleCompleteTask() {
      // 使用任务信息作为当前完成任务
      this.currentCompleteTask = {
        taskId: this.taskInfo.taskId,
        taskName: this.taskInfo.taskName
      }
      this.completeTaskDialogVisible = true
    },

    // 任务完成回调
    onTaskCompleted() {
      // 重新加载任务信息
      this.loadTaskInfo()
    },

    // 查看任务完成凭证
    async handleViewTaskCredentials() {
      this.credentialsLoading = true

      try {
        const [err, res] = await client.customerGetTaskCredentials(this.taskInfo.taskId)

        if (err) {
          handleError(err)
          return
        }

        // 设置凭证数据
        this.credentialsData = {
          taskName: res.data.taskName || this.taskInfo.taskName,
          taskFinishTime: res.data.taskFinishTime,
          taskCredentials: res.data.taskCredentials || []
        }

        // 显示对话框
        this.credentialsDialogVisible = true
      } catch (error) {
        console.error('获取任务完成凭证失败：', error)
        this.$message.error('获取任务完成凭证失败，请重试')
      } finally {
        this.credentialsLoading = false
      }
    },

    handleAssignPersonnel() {
      this.assignDialogVisible = true
      this.fileList = []
    },

    // 批量导出人员
    async handleBatchExport() {
      // 防重点击
      if (this.exportLoading) {
        return
      }

      this.exportLoading = true

      try {
        const taskId = this.$route.params.id

        // 使用fetch API携带token下载文件
        const token = this.headerToken.Authorization
        const response = await fetch(`${window.env?.apiPath}/api/public/task/exportTaskLabor`, {
          method: 'POST',
          headers: {
            'Authorization': token,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            filters: {
              taskId: parseInt(taskId)
            }
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        // 获取文件blob
        const blob = await response.blob()

        let fileName = '任务人员信息.zip'

        // 创建下载链接
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = fileName
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // 清理URL对象
        window.URL.revokeObjectURL(url)

        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出失败：', error)
        this.$message.error('导出失败，请重试')
      } finally {
        this.exportLoading = false
      }
    },

    // 下载模板
    async downloadTemplate() {
      this.downloadLoading = true

      try {
        // 使用fetch API携带token下载文件
        const token = this.headerToken.Authorization
        const response = await fetch(`${window.env?.apiPath}/api/public/task/download/template`, {
          method: 'GET',
          headers: {
            'Authorization': token,
            'Content-Type': 'application/octet-stream'
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        // 获取文件blob
        const blob = await response.blob()

        // 创建下载链接
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = '指派人员模板.xlsx'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // 清理URL对象
        window.URL.revokeObjectURL(url)

        this.$message.success('模板下载成功')
      } catch (error) {
        console.error('下载模板失败：', error)
        this.$message.error('下载模板失败')
      } finally {
        this.downloadLoading = false
      }
    },

    // 文件选择变化
    handleFileChange(file, fileList) {
      // 只保留最后一个文件（覆盖上传）
      if (fileList.length > 1) {
        this.fileList = [fileList[fileList.length - 1]]
      } else {
        this.fileList = fileList
      }
      console.log('文件列表变化：', this.fileList)
    },

    // 文件移除
    handleFileRemove(file, fileList) {
      this.fileList = fileList
      console.log('文件移除后列表：', fileList)
    },

    // 文件超出限制
    handleExceed(files, fileList) {
      // 清除之前的文件
      this.$refs.upload.clearFiles()
      // 添加新文件
      const file = files[0]
      this.$refs.upload.handleStart(file)
    },

    // 取消指派
    cancelAssign() {
      this.assignDialogVisible = false
      this.fileList = []
    },

    // 确认指派
    async confirmAssign() {
      // 检查文件列表是否为空
      if (!this.fileList || this.fileList.length === 0) {
        this.$message.warning('请选择要上传的文件')
        return
      }

      // 检查文件对象是否有效
      const fileItem = this.fileList[0]
      if (!fileItem || !fileItem.raw) {
        this.$message.warning('请选择有效的文件')
        return
      }

      const file = fileItem.raw

      // 再次检查文件对象
      if (!file || file.size === 0) {
        this.$message.warning('文件无效，请重新选择文件')
        return
      }

      // 检查文件类型
      const allowedTypes = [
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ]
      if (!allowedTypes.includes(file.type)) {
        this.$message.warning('只支持 .xls 和 .xlsx 格式的文件')
        return
      }

      // 检查文件大小（10MB）
      if (file.size > 10 * 1024 * 1024) {
        this.$message.warning('文件大小不能超过10MB')
        return
      }

      this.uploadLoading = true

      try {
        const formData = new FormData()
        // 后端期望的是MultipartFile[]数组，所以使用file[0]的形式
        formData.append('file', file)
        formData.append('taskId', this.$route.params.id)

        const [err, res] = await client.importTaskLabor({
          body: formData,
          requestInterceptor(resource, options) {
            delete options.headers['Content-Type']
            return [null, resource, options]
          }
        })

        if (err) {
          handleError(err)
          return
        }

        handleSuccess('指派人员成功')
        this.assignDialogVisible = false
        this.fileList = []
        // 重新加载人员列表
        this.loadPersonnelList()
      } catch (error) {
        console.error('上传失败：', error)
        this.$message.error('上传失败，请重试')
      } finally {
        this.uploadLoading = false
      }
    },

    handleViewDelivery(row) {
      this.$message.info('查看交付功能待实现')
    },

    // 完成任务
    handleCompletePersonTask(row) {
      this.$confirm(
       '<span style="font-size: 15px;color: black;">是否确定将该人员标记为完成任务？</span><br>' +
       '<span style="font-size: 13px;">确定后该人员的任务状态将变为已完成，该操作无法撤销。</span>',
       '完成任务',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: true
        }
      ).then(async () => {
        await this.finishTaskLabor(row)
      }).catch(() => {
      })
    },

    // 完成任务接口调用
    async finishTaskLabor(row) {
      this.finishTaskLoading = true

      try {
        const [err] = await client.finishTaskLabor({
          body: {
            taskLaborId: row.id
          }
        })

        if (err) {
          handleError(err)
          return
        }

        handleSuccess('任务完成成功')
        // 重新加载人员列表
        this.loadPersonnelList()
      } catch (error) {
        console.error('完成任务失败：', error)
        this.$message.error('完成任务失败，请重试')
      } finally {
        this.finishTaskLoading = false
      }
    },

    handleAuditDelivery(row) {
      this.$message.info('审核交付功能待实现')
    },

    // 审核人员
    handleAuditPersonnel(row) {
      this.currentAuditRow = row
      this.auditForm = {
        auditResult: true, // 默认选择审核通过
        auditResultDesc: ''
      }
      this.auditPersonnelDialogVisible = true
    },

    // 取消审核
    cancelAuditPersonnel() {
      this.auditPersonnelDialogVisible = false
      this.currentAuditRow = null
      this.auditForm = {
        auditResult: true, // 默认选择审核通过
        auditResultDesc: ''
      }
    },

    // 确认审核
    async confirmAuditPersonnel() {
      // 验证审核结果
      if (this.auditForm.auditResult === null || this.auditForm.auditResult === undefined) {
        this.$message.warning('请选择审核结果')
        return
      }

      // 如果是拒绝，验证拒绝原因
      if (this.auditForm.auditResult === false && !this.auditForm.auditResultDesc.trim()) {
        this.$message.warning('审核拒绝时必须填写拒绝原因')
        return
      }

      this.auditPersonnelLoading = true

      try {
        const [err] = await client.auditTaskLabor({
          body: {
            taskLaborId: this.currentAuditRow.id,
            auditResult: this.auditForm.auditResult,
            auditResultDesc: this.auditForm.auditResultDesc
          }
        })

        if (err) {
          handleError(err)
          return
        }

        handleSuccess('审核成功')
        this.auditPersonnelDialogVisible = false
        this.currentAuditRow = null
        this.auditForm = {
          auditResult: true, // 默认选择审核通过
          auditResultDesc: ''
        }
        // 重新加载人员列表
        this.loadPersonnelList()
      } catch (error) {
        console.error('审核失败：', error)
        this.$message.error('审核失败，请重试')
      } finally {
        this.auditPersonnelLoading = false
      }
    },

    setTableHeight() {
      const windowHeight = window.innerHeight;
      const summaryContainer = this.$el.querySelector('.summary-container');
      const summaryHeight = summaryContainer ? summaryContainer.offsetHeight : 0;
      const searchContainer = this.$el.querySelector('.search-container');
      const searchHeight = searchContainer ? searchContainer.offsetHeight : 0;
      const tableHeader = this.$el.querySelector('.table-header');
      const tableHeaderHeight = tableHeader ? tableHeader.offsetHeight : 0;
      const pagination = this.$el.querySelector('.pagination-container');
      const paginationHeight = pagination ? pagination.offsetHeight : 40;
      const padding = 80;

      const availableHeight = windowHeight - summaryHeight - searchHeight - tableHeaderHeight - paginationHeight - padding;

      if (this.tableData.length <= 5) {
        this.tableHeight = 500;
      } else {
        const minHeight = 300;
        const maxHeight = windowHeight - summaryHeight - searchHeight - tableHeaderHeight - paginationHeight - padding - 5;
        this.tableHeight = Math.min(maxHeight, Math.max(availableHeight, minHeight));
      }

      this.tableHeight = Math.floor(this.tableHeight);
    }
  }
}
</script>

<style scoped>
.task-personnel-detail-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  background-color: #f5f7fa;
  padding: 12px;
  box-sizing: border-box;
}

.detail-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.summary-container {
  background: #fff;
  padding: 12px 20px;
  margin-bottom: 10px;
  border-radius: 8px;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.05);
  flex: 0 0 auto;
}

.summary-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.summary-left {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 8px 24px;
}

.summary-item {
  line-height: 28px;
  font-size: 14px;
  color: #606266;
  display: flex;
  align-items: center;
  min-width: 200px;
}

.summary-item label {
  display: inline-block;
  width: 100px;
  text-align: right;
  color: #909399;
  margin-right: 8px;
  flex-shrink: 0;
}

.summary-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
  margin-left: 20px;
}

.search-container {
  background: #fff;
  padding: 10px;
  border-radius: 8px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.search-form .el-form-item {
  margin: 0;
}

.search-form .el-form-item .el-form-item__content {
  width: 200px;
}

.table-container {
  background: #fff;
  padding: 16px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.05);
  flex: 1;
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-bottom: 12px;
}

.table-actions .el-button {
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 13px;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.table-actions .el-button--primary {
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
}

.table-actions .el-button--primary:hover {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  transform: translateY(-1px);
}

.el-table {
  margin-bottom: 5px;
  border: none;
}

.el-table th,
.el-table--medium th {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 550;
  text-align: left;
  padding: 4px 0;
  border-bottom: 1px solid #EBEEF5;
  font-size: 13px;
  height: 18px;
  line-height: 18px;
}

.el-table td,
.el-table--medium td {
  padding: 4px 0;
  color: #606266;
  font-size: 13px;
  height: 18px;
  line-height: 18px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.action-buttons .el-button {
  margin: 0;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 3px;
  transition: all 0.2s;
  min-width: 48px;
  height: 24px;
  line-height: 16px;
}

.action-buttons .el-button--text {
  background: transparent;
  border: none;
  padding: 4px 8px;
}

.action-buttons .el-button--text:hover {
  background: #ecf5ff;
  transform: translateY(-1px);
}

.empty-data {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 40px 0;
}

.pagination-container {
  padding: 12px 0 0;
  text-align: right;
  background: #fff;
  margin-top: auto;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .task-personnel-detail-container {
    padding: 12px;
  }

  .summary-container {
    padding: 12px;
  }

  .summary-content {
    flex-direction: column;
    gap: 16px;
  }

  .summary-left {
    flex-direction: column;
    gap: 8px;
  }

  .summary-item {
    min-width: auto;
  }

  .summary-item label {
    width: 70px;
  }

  .summary-actions {
    margin-left: 0;
    justify-content: flex-start;
  }

  .table-container {
    padding: 12px;
  }
}

/* 指派人员对话框样式 */
.assign-personnel-dialog {
  border-radius: 8px;
}

.assign-content {
  padding: 20px 0;
}

.assign-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
}

.assign-title {
  font-size: 14px;
  color: #606266;
}

.upload-area {
  margin-top: 20px;
}

.upload-demo {
  width: 100%;
  display: flex;
  align-content: center;
  flex-wrap: wrap;
  flex-direction: column;
}

.upload-demo .el-upload {
  width: 100%;
}


.upload-demo .el-upload-dragger {
  width: 100%;
  height: 180px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.2s;
}

.upload-demo .el-upload-dragger:hover {
  border-color: #409eff;
}

.upload-demo .el-upload-dragger .el-icon-upload {
  font-size: 67px;
  color: #c0c4cc;
  margin: 40px 0 16px;
  line-height: 50px;
}

.upload-demo .el-upload__text {
  color: #606266;
  font-size: 14px;
  text-align: center;
}

.upload-demo .el-upload__tip {
  font-size: 12px;
  color: #909399;
  margin-top: 7px;
  text-align: center;
}

.dialog-footer {
  text-align: center;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

/* 审核人员对话框样式 */
.audit-personnel-dialog {
  border-radius: 8px;
}

.audit-content {
  padding: 20px 0;
}

.audit-content .el-form-item {
  margin-bottom: 20px;
}

.audit-content .el-select {
  width: 100%;
}
</style>
