<template>
  <div class="personalProfileBasicInfo">
  </div>
</template>
<script>
import handleError from 'kit/helpers/handleErrorH5'
import makeClient from 'kit/services/operateLabor/makeClient'
const client = makeClient()

export default {
  data() {
    return {
      info: null
    }
  },
  created() {
    this,init()
  },
  methods: {
    async init() {
      const [err, r] = await client.api()
    }
  }
}
</script>