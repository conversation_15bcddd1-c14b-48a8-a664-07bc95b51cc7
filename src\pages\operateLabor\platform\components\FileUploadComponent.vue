<template>
  <div class="file-upload-container">
    <!-- 文件预览网格 -->
    <div class="file-grid">
      <!-- 已上传的文件 -->
      <div
        v-for="(file, index) in fileList"
        :key="index"
        class="file-preview-item"
      >
        <!-- 图片预览 -->
        <div v-if="isImageFile(file)" class="image-preview">
          <div class="preview-image-container">
            <img :src="getFilePreviewUrl(file)" :alt="file.name" class="preview-image" />
          </div>
          <div class="file-info">
            <span class="file-name" :title="file.name">{{ file.name }}</span>
          </div>
          <div class="file-overlay">
            <div class="file-actions">
              <i class="el-icon-zoom-in" @click="previewImage(file)" title="预览"></i>
              <i v-if="!readonly" class="el-icon-delete" @click="removeFile(index)" title="删除"></i>
            </div>
          </div>
        </div>

        <!-- 非图片文件预览 -->
        <div v-else class="file-preview">
          <div class="file-icon-container">
            <i
              :class="getFileIcon(file).icon"
              class="file-type-icon"
              :style="{ color: getFileIcon(file).color }"
            ></i>
          </div>
          <div class="file-info">
            <span class="file-name" :title="file.name">{{ file.name }}</span>
          </div>
          <div class="file-overlay">
            <div class="file-actions">
              <i v-if="!readonly" class="el-icon-delete" @click="removeFile(index)" title="删除"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 上传按钮 -->
      <div v-if="fileList.length < 10 && !readonly" class="upload-item">
        <el-upload
          ref="upload"
          class="file-upload"
          :headers="headerToken"
          :file-list="fileList"
          :auto-upload="false"
          :on-change="onFileChange"
          :on-remove="onFileRemove"
          :before-upload="beforeUpload"
          accept=".pdf,.png,.jpg,.jpeg"
          :action="uploadUrl"
          :show-file-list="false"
          multiple
          drag
        >
          <div class="upload-box">
            <i class="el-icon-plus upload-icon"></i>
            <span class="upload-text">点击/拖拽文件至此区域</span>
          </div>
        </el-upload>
      </div>
    </div>
    
    <!-- 图片预览弹窗 -->
    <el-dialog
      :visible.sync="previewVisible"
      :show-close="true"
      :modal="true"
      :close-on-click-modal="true"
      width="800px"
      custom-class="image-preview-dialog"
      :title="previewImageName"
    >
      <img
        v-if="previewImageUrl"
        :src="previewImageUrl"
        style="max-width: 100%; max-height: 70vh"
      />
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '../../../../helpers/token'

export default {
  name: 'FileUploadComponent',
  props: {
    initialFiles: {
      type: Array,
      default: () => []
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fileList: [],
      headerToken: {
        Authorization: `Bearer ${getToken()}`
      },
      previewVisible: false,
      previewImageUrl: '',
      previewImageName: ''
    }
  },
  computed: {
    uploadUrl() {
      return `${window.env?.apiPath}/api/public/uploadFile`
    }
  },
  watch: {
    initialFiles: {
      handler(newFiles) {
        if (newFiles && newFiles.length > 0) {
          this.fileList = [...newFiles]
        }
      },
      immediate: true
    }
  },
  methods: {
    // 获取文件ID数组，用于提交时调用
    async getFileIds() {
      const fileIds = []
      
      // 上传所有未上传的文件
      for (const file of this.fileList) {
        if (!file.fileId && file.raw) {
          try {
            const response = await this.uploadSingleFile(file)
            if (response.success && response.data) {
              file.fileId = response.data.fileId
              fileIds.push(response.data.fileId.toString())
            }
          } catch (error) {
            console.error('文件上传失败：', error)
            throw new Error(`文件 ${file.name} 上传失败`)
          }
        } else if (file.fileId) {
          fileIds.push(file.fileId.toString())
        }
      }
      
      return fileIds
    },

    async uploadSingleFile(file) {
      return new Promise((resolve, reject) => {
        const formData = new FormData()
        formData.append('file', file.raw)

        const xhr = new XMLHttpRequest()
        xhr.open('POST', this.uploadUrl)
        xhr.setRequestHeader('Authorization', this.headerToken.Authorization)

        xhr.onload = () => {
          if (xhr.status === 200) {
            try {
              const response = JSON.parse(xhr.responseText)
              resolve(response)
            } catch (e) {
              reject(new Error('解析响应失败'))
            }
          } else {
            reject(new Error(`上传失败: ${xhr.status}`))
          }
        }

        xhr.onerror = () => {
          reject(new Error('网络错误'))
        }

        xhr.send(formData)
      })
    },

    beforeUpload(file) {
      const isValidType = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'].includes(file.type)
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isValidType) {
        this.$message.error('只能上传 JPG、PNG、JPEG、PDF 格式的文件!')
        return false
      }
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB!')
        return false
      }
      return true
    },

    onFileChange(file, fileList) {
      // 限制文件数量
      if (fileList.length > 10) {
        this.$message.warning('最多只能上传10个文件')
        fileList.splice(10) // 移除超出的文件
      }

      // 为新添加的文件设置预览URL
      if (file && this.isImageFile(file)) {
        file.previewUrl = this.getFilePreviewUrl(file)
      }

      this.fileList = fileList
      this.$emit('files-change', this.fileList)
    },

    onFileRemove(file, fileList) {
      this.fileList = fileList
      this.$emit('files-change', this.fileList)
    },

    removeFile(index) {
      this.fileList.splice(index, 1)
      this.$emit('files-change', this.fileList)
    },

    // 文件处理相关方法
    isImageFile(file) {
      const imageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
      return imageTypes.includes(file.raw?.type) || /\.(jpg|jpeg|png|gif|webp)$/i.test(file.name)
    },

    getFileIcon(file) {
      const fileName = file.name.toLowerCase()

      // PDF文件
      if (fileName.endsWith('.pdf')) {
        return { icon: 'el-icon-document-copy', color: '#5470c6' }
      }
      // 图片文件
      else if (this.isImageFile(file)) {
        return { icon: 'el-icon-picture-outline', color: '#9c27b0' }
      }
      // 其他文件
      else {
        return { icon: 'el-icon-tickets', color: '#409eff' }
      }
    },

    getFilePreviewUrl(file) {
      if (file.raw) {
        return URL.createObjectURL(file.raw)
      }
      return ''
    },

    previewImage(file) {
      // 使用el-dialog组件预览图片
      this.previewImageUrl = this.getFilePreviewUrl(file)
      this.previewImageName = file.name
      this.previewVisible = true
    },

    // 重置文件列表
    resetFiles() {
      this.fileList = []
      this.$nextTick(() => {
        if (this.$refs.upload) {
          this.$refs.upload.clearFiles()
        }
      })
    }
  }
}
</script>

<style scoped>
.file-upload-container {
  width: 100%;
  box-sizing: border-box;
}

.file-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-start;
}

.file-preview-item {
  position: relative;
  width: 160px;
  height: 140px;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #409eff;

    .file-overlay {
      opacity: 1;
    }
  }
}

.image-preview {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .preview-image-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    padding: 8px;

    .preview-image {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      border-radius: 4px;
    }
  }

  .file-info {
    padding: 8px 12px;
    background: #fff;
    border-top: 1px solid #f0f0f0;

    .file-name {
      font-size: 12px;
      color: #606266;
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.file-preview {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .file-icon-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    padding: 20px;

    .file-type-icon {
      font-size: 48px;
    }
  }

  .file-info {
    padding: 8px 12px;
    background: #fff;
    border-top: 1px solid #f0f0f0;

    .file-name {
      font-size: 12px;
      color: #606266;
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.file-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 8px;

  .file-actions {
    display: flex;
    gap: 16px;

    i {
      cursor: pointer;
      font-size: 20px;
      padding: 8px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
      }
    }
  }
}

.upload-item {
  width: 160px !important;
  height: 140px !important;
  flex-shrink: 0; /* 防止flex容器压缩上传区域 */
}

.file-upload {
  width: 100% !important;
  height: 100% !important;
  position: relative; /* 确保内部元素相对此容器定位 */
}

/* 拖拽上传区域样式控制 */
.upload-box {
  width: 160px !important;
  height: 140px !important;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafbfc;
  box-sizing: border-box; /* 确保边框不影响尺寸 */

  &:hover {
    border-color: #409eff;
    background: #f0f9ff;

    .upload-icon {
      color: #409eff;
    }

    .upload-text {
      color: #409eff;
    }
  }

  .upload-icon {
    font-size: 28px;
    color: #c0c4cc;
    margin-bottom: 8px;
    transition: color 0.3s ease;
  }

  .upload-text {
    font-size: 12px;
    color: #909399;
    text-align: center;
    line-height: 1.4;
    transition: color 0.3s ease;
    padding: 0 8px; /* 防止文本溢出 */
  }
}

/* 覆盖element-ui的默认拖拽样式 */
::v-deep .el-upload-dragger {
  width: 160px !important;
  height: 140px !important;
  padding: 0 !important;
  border: none !important;
}

/* 图片预览对话框样式 */
::v-deep .image-preview-dialog {
  text-align: center;
}

::v-deep .image-preview-dialog .el-dialog__body {
  padding: 20px;
}

::v-deep .image-preview-dialog .el-dialog__header {
  padding: 15px 20px;
}

::v-deep .image-preview-dialog img {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
}
</style>
