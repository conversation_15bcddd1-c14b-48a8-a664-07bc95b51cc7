<template>
  <div>
    <div class="type-bar">
      <span
        :class="{ active: currType === 'confirm' }"
        @click="handleChangeType('confirm')"
        >待确认</span
      >
      <span
        :class="{ active: currType === 'progressing' }"
        @click="handleChangeType('progressing')"
        >进行中</span
      >
      <span
        :class="{ active: currType === 'completed' }"
        @click="handleChangeType('completed')"
        >已完成</span
      >
      <span
        :class="{ active: currType === 'refused' }"
        @click="handleChangeType('refused')"
        >已拒绝</span
      >
    </div>
    <div
      style="
        min-height: calc(100vh - 66px);
        overflow: auto;
        box-sizing: border-box;
        padding: 20px 15px 10px 15px;
        background: #f8f8f8;
      "
    >
      <div v-if="taskList.length">
        <div class="task" v-for="(item, index) in taskList" :key="index" @click="handleDetail(item)">
          <div
            style="
              display: flex;
              justify-content: space-between;
              align-items: center;
            "
          >
            <div style="display: flex; flex-direction: column">
              <span
                style="
                  width: 200px;
                  word-break: break-all;
                  word-wrap: break-word;
                  white-space: pre-wrap;
                  font-size: 15px;
                  font-weight: 600;
                  color: #262935;
                  margin-top: 7px;
                "
                >营销服务7</span
              >
              <span
                style="
                  width: 200px;
                  word-break: break-all;
                  word-wrap: break-word;
                  white-space: pre-wrap;
                  font-size: 13px;
                  color: #71788f;
                  margin-top: 10px;
                "
                >三角洲行动有限公司</span
              >
              <div style="margin-top: 10px">
                <!-- <img width="13px" src="" alt="" /> -->
                <span style="font-size: 13px; color: #262935"
                  >POS营销及推广</span
                >
              </div>
            </div>
            <div style="font-size: 20px; font-weight: 600; color: #0082ff">
              面试
            </div>
          </div>
          <div style="height: 1px; background: #f0f2f7; margin-top: 20px"></div>
          <div
            style="
              margin-top: 10px;
              display: flex;
              justify-content: space-between;
            "
          >
            <span style="font-size: 13px; color: #71788f; margin-top: 8px"
              >2025.7.31 - 2025.12.31</span
            >
            <span>{{ formatter(item.status) }}</span>
          </div>
        </div>
      </div>
      <div class="no-content" v-else>
        <img
          style="width: 40%"
          src="https://olading-static-resource.oss-cn-beijing.aliyuncs.com/olading-mini-image/olading-front-h5/no-data.png"
        />
        <div style="text-align: center; margin-top: 10px">暂无任务</div>
      </div>
    </div>
  </div>
</template>
<script>
import handleError from '../../../helpers/handleErrorH5'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  data() {
    return {
      currType: 'confirm',
      taskList: []
    }
  },
  methods: {
    formatter(status) {
      if (status === 'confirm') {
        return '待确认'
      }
      if (status === 'progressing') {
        return '进行中'
      }
      if (status === 'completed') {
        return '已完成'
      }
      if (status === 'refused') {
        return '已拒绝'
      }
    },
    handleChangeType(type) {
      this.currType = type
      this.fetchList()
    },
    async fetchList() {
      this.taskList = []
      const [err, r] = await client.getMyTaskList({
        type: this.currType
      })
      if (err) return handleError(err)
    },
    handleDetail(item) {
      this.$router.push({
        path: '/myTask/detail',
        query: {
          id: item.id
        }
      })
    },
  }
}
</script>
<style scoped>
.type-bar {
  display: flex;
  justify-content: space-between;
  font-size: 15px;
  padding: 20px 10px;
  height: 26px;
}
.type-bar span {
  text-align: center;
  margin-right: 23px;
  font-weight: 400;
  height: 25px;
}
.active {
  border-bottom: 2px solid #0082ff;
  color: #0082ff;
}
.task {
  padding: 15px;
  background: #fff;
  margin-bottom: 10px;
  position: relative;
  border-radius: 4px;
}
.no-content {
  height: 450px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style>
