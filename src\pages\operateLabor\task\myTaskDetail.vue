<template>
  <div>
    <div style="height: calc(100vh - 80px);overflow: auto;">

    <Cell>
      <div
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        <h2>营销服务7</h2>
        <div>
          <Icon name="calendar-o" />
          <span>2025.07.31</span>
        </div>
      </div>
      <div style="display: flex; flex-direction: column">
        <div style="font-size: 23px; color: #0082fd">面议</div>
        <span>任务时间：2025.07.31 - 2025.12.31</span>
      </div>
    </Cell>
    <Cell>
      <h2>用工企业</h2>
      <span class="name">柏仟信***（云南）有限公司</span>
    </Cell>
    <Cell>
      <h2>派工企业</h2>
      <span class="name">柏仟信***（云南）有限公司</span>
    </Cell>
    <Cell>
      <div
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        <h2>人员要求</h2>
        <span>招收50人</span>
      </div>
      <span class="name">柏仟信***（云南）有限公司</span>
    </Cell>
    <Cell>
      <h2>任务地点</h2>
      <span class="name">柏仟信***（云南）有限公司</span>
    </Cell>
    <Cell>
      <h2>任务描述</h2>
      <span class="name">柏仟信***（云南）有限公司</span>
    </Cell>
    </div>

    <div
      style="
        width: 100vw;
        height: 40px;
        position: fixed;
        bottom: 0;
        padding: 20px 0;
      "
    >
      <div class="btn" @click="handleApply">申请任务</div>
    </div>
  </div>
</template>
<script>
import { Cell, Icon } from 'vant'

export default {
  components: {
    Cell,
    Icon
  },
  data() {
    return {}
  },
  methods: {
    handleApply() {}
  }
}
</script>
<style scoped>
.name {
  color: #73788c;
}
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #0082fe;
  color: #fff;
  border-radius: 20px;
  margin: 0 30px;
  padding: 10px;
}
</style>
